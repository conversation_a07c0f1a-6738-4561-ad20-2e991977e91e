import React from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { Toaster } from 'sonner'
import { ThemeProvider } from '@/components/ui/theme-provider'
import { useAuth } from '@/hooks/useAuth'

// Auth Pages - Passwordless WhatsApp Authentication
import { WhatsAppLoginPage } from '@/pages/auth/WhatsAppLoginPage'
import { WhatsAppSignUpPage } from '@/pages/auth/WhatsAppSignUpPage'
import { WhatsAppVerifyPage } from '@/pages/auth/WhatsAppVerifyPage'

// Onboarding Pages - Setup Flow
import { OnboardingPage } from '@/pages/OnboardingPage'

// Portal Pages - Enterprise Business Application
import { AnalyticsDashboard } from '@/pages/portal/AnalyticsDashboard'
import { CRMPage } from '@/pages/portal/CRMPage'
import ChatPage from '@/pages/portal/ChatPage'
import { WhatsAppConnectionsPage } from '@/pages/portal/WhatsAppConnectionsPage'
import { ShopifyIntegrationPage } from '@/pages/portal/ShopifyIntegrationPage'
import { PortalLayout } from '@/components/portal/PortalLayout'
import { PhoneNumbersPage } from '@/pages/portal/PhoneNumbersPage'

// ============================================================================
// Loading Component
// ============================================================================

const LoadingScreen: React.FC = () => (
  <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-white to-slate-50">
    <div className="text-center space-y-6">
      <div className="relative">
        <div className="animate-spin rounded-full h-12 w-12 border-4 border-slate-200 border-t-blue-600 mx-auto"></div>
        <div className="absolute inset-0 rounded-full border-4 border-transparent border-t-blue-400 animate-ping opacity-20"></div>
      </div>
      <div className="space-y-2">
        <h3 className="text-lg font-semibold text-slate-900">Loading Notefy Portal</h3>
        <p className="text-slate-600">Preparing your enterprise workspace...</p>
      </div>
    </div>
  </div>
)

// ============================================================================
// App Router Component
// ============================================================================

const AppRouter: React.FC = () => {
  const { isAuthenticated, isLoading } = useAuth()

  // Show enterprise loading screen while checking authentication
  if (isLoading) {
    return <LoadingScreen />
  }

  return (
    <Routes>
      {/* ================================================================ */}
      {/* Public Routes */}
      {/* ================================================================ */}
      
      {/* ================================================================ */}
      {/* Authentication Routes */}
      {/* ================================================================ */}
      <Route
        path="/auth/login"
        element={isAuthenticated ? <Navigate to="/onboarding" replace /> : <WhatsAppLoginPage />}
      />
      <Route
        path="/auth/signup"
        element={isAuthenticated ? <Navigate to="/onboarding" replace /> : <WhatsAppSignUpPage />}
      />
      <Route
        path="/auth/verify"
        element={<WhatsAppVerifyPage />}
      />
      
      {/* ================================================================ */}
      {/* Onboarding Routes - Setup Flow */}
      {/* ================================================================ */}
      <Route
        path="/onboarding"
        element={isAuthenticated ? <OnboardingPage /> : <Navigate to="/auth/login" replace />}
      />
      
      {/* ================================================================ */}
      {/* Full-Screen Chat Route - Outside Portal Layout */}
      {/* ================================================================ */}
      <Route
        path="/portal/chat"
        element={isAuthenticated ? <ChatPage /> : <Navigate to="/auth/login" replace />}
      />
      
      {/* ================================================================ */}
      {/* Enterprise Portal Routes - Main Business Application */}
      {/* ================================================================ */}
      <Route
        path="/portal"
        element={isAuthenticated ? <PortalLayout /> : <Navigate to="/auth/login" replace />}
      >
        <Route index element={<Navigate to="/portal/analytics" replace />} />
        <Route path="analytics" element={<AnalyticsDashboard />} />
        <Route path="shopify-integration" element={<ShopifyIntegrationPage />} />
        <Route path="whatsapp-connections" element={<WhatsAppConnectionsPage />} />
        <Route path="crm" element={<CRMPage />} />
        <Route path="phone-numbers" element={<PhoneNumbersPage />} />
      </Route>

      {/* ================================================================ */}
      {/* Legacy Route Redirects - Maintain Backward Compatibility */}
      {/* ================================================================ */}
      <Route
        path="/dashboard"
        element={<Navigate to="/portal/analytics" replace />}
      />
      <Route
        path="/notes"
                          element={<Navigate to="/portal/analytics" replace />}
      />
      <Route
        path="/profile"
        element={<Navigate to="/portal/analytics" replace />}
      />

      {/* ================================================================ */}
      {/* Default Routes */}
      {/* ================================================================ */}
      <Route 
        path="/" 
        element={<Navigate to={isAuthenticated ? "/onboarding" : "/auth/login"} replace />} 
      />
      <Route 
        path="*" 
        element={<Navigate to={isAuthenticated ? "/onboarding" : "/auth/login"} replace />} 
      />
    </Routes>
  )
}

// ============================================================================
// Main App Component
// ============================================================================

const App: React.FC = () => {
  return (
    <ThemeProvider defaultTheme="light" storageKey="notefy-portal-theme">
      <Router>
        <div className="h-full bg-background">
          <AppRouter />
          
          {/* Enterprise Toast Notifications */}
          <Toaster 
            position="top-right" 
            richColors 
            expand={true}
            duration={4000}
            toastOptions={{
              className: 'bg-card border-border text-card-foreground',
            }}
          />
        </div>
      </Router>
    </ThemeProvider>
  )
}

export default App
