// WhatsApp-style Chat Header Component
// Shows contact info, online status, and action buttons

import React from 'react';
import { cn } from '../../lib/utils';
import { useChatStore, useCurrentChat, useIsTyping } from '../../stores/chatStore';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { Button } from '../ui/button';
import { 
  ArrowLeft, 
  Phone, 
  Video, 
  MoreVertical,
  Search,
  Paperclip
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';
import { formatDistanceToNow } from 'date-fns';

interface ChatHeaderProps {
  className?: string;
}

export const ChatHeader: React.FC<ChatHeaderProps> = ({ className }) => {
  const { clearSelection } = useChatStore();
  const currentChat = useCurrentChat();
  const isTyping = useIsTyping(currentChat?.id || '');

  if (!currentChat) return null;

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  const getLastSeenText = () => {
    if (currentChat.isOnline) {
      return 'online';
    } else if (currentChat.lastSeen) {
      const distance = formatDistanceToNow(currentChat.lastSeen, { addSuffix: true });
      return `last seen ${distance}`;
    }
    return 'offline';
  };

  const handleBack = () => {
    clearSelection();
  };

  return (
    <div className={cn(
      'flex items-center justify-between p-4',
      'bg-chat-header dark:bg-chat-header-dark',
      'border-b border-chat-border dark:border-chat-border-dark',
      className
    )}>
      {/* Left side: Back button, avatar, and contact info */}
      <div className="flex items-center gap-3 flex-1 min-w-0">
        {/* Back button (mobile) */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleBack}
          className="md:hidden w-8 h-8 p-0 text-chat-text-secondary dark:text-chat-text-secondary-dark hover:bg-chat-hover dark:hover:bg-chat-hover-dark rounded-full"
          aria-label="Back to chats"
        >
          <ArrowLeft className="w-5 h-5" />
        </Button>

        {/* Avatar */}
        <div className="relative flex-shrink-0">
          <Avatar className="w-10 h-10 cursor-pointer">
            <AvatarImage src={currentChat.avatar} alt={currentChat.name} />
            <AvatarFallback className="bg-chat-primary text-chat-text-on-primary text-sm font-medium">
              {getInitials(currentChat.name)}
            </AvatarFallback>
          </Avatar>
          
          {/* Online indicator */}
          {currentChat.isOnline && (
            <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-chat-online border-2 border-chat-header dark:border-chat-header-dark rounded-full" />
          )}
        </div>

        {/* Contact info */}
        <div className="flex-1 min-w-0 cursor-pointer">
          <h2 className={cn(
            'font-medium text-base truncate',
            'text-chat-text-primary dark:text-chat-text-primary-dark'
          )}>
            {currentChat.name}
          </h2>
          <p className={cn(
            'text-xs truncate',
            isTyping 
              ? 'text-chat-typing dark:text-chat-typing' 
              : 'text-chat-text-secondary dark:text-chat-text-secondary-dark'
          )}>
            {isTyping ? 'typing...' : getLastSeenText()}
          </p>
        </div>
      </div>

      {/* Right side: Action buttons */}
      <div className="flex items-center gap-1 flex-shrink-0">
        {/* Video call */}
        <Button
          variant="ghost"
          size="sm"
          className="w-10 h-10 p-0 text-chat-text-secondary dark:text-chat-text-secondary-dark hover:bg-chat-hover dark:hover:bg-chat-hover-dark rounded-full"
          title="Video call"
        >
          <Video className="w-5 h-5" />
        </Button>

        {/* Voice call */}
        <Button
          variant="ghost"
          size="sm"
          className="w-10 h-10 p-0 text-chat-text-secondary dark:text-chat-text-secondary-dark hover:bg-chat-hover dark:hover:bg-chat-hover-dark rounded-full"
          title="Voice call"
        >
          <Phone className="w-5 h-5" />
        </Button>

        {/* Search */}
        <Button
          variant="ghost"
          size="sm"
          className="hidden sm:flex w-10 h-10 p-0 text-chat-text-secondary dark:text-chat-text-secondary-dark hover:bg-chat-hover dark:hover:bg-chat-hover-dark rounded-full"
          title="Search"
        >
          <Search className="w-5 h-5" />
        </Button>

        {/* Menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="w-10 h-10 p-0 text-chat-text-secondary dark:text-chat-text-secondary-dark hover:bg-chat-hover dark:hover:bg-chat-hover-dark rounded-full"
              title="Menu"
            >
              <MoreVertical className="w-5 h-5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem>
              Contact info
            </DropdownMenuItem>
            <DropdownMenuItem>
              Select messages
            </DropdownMenuItem>
            <DropdownMenuItem className="sm:hidden">
              <Search className="w-4 h-4 mr-2" />
              Search
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              Mute notifications
            </DropdownMenuItem>
            <DropdownMenuItem>
              Disappearing messages
            </DropdownMenuItem>
            <DropdownMenuItem>
              Clear chat
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="text-red-600">
              Delete chat
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
};

export default ChatHeader;
