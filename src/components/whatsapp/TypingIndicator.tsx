// WhatsApp-style Typing Indicator Component
// Animated dots showing when someone is typing

import React from 'react';
import { cn } from '../../lib/utils';

interface TypingIndicatorProps {
  contactName?: string;
  className?: string;
}

export const TypingIndicator: React.FC<TypingIndicatorProps> = ({ 
  contactName,
  className 
}) => {
  return (
    <div className={cn('group', className)}>
      <div className={cn(
        'relative px-3 py-2 max-w-full shadow-sm',
        'bg-chat-inbound dark:bg-chat-inbound-dark',
        'text-chat-inbound-text dark:text-chat-inbound-text-dark',
        'border border-chat-border/20 dark:border-chat-border-dark/20',
        'rounded-lg rounded-bl-sm',
        'transition-colors duration-200'
      )}>
        {/* Typing animation */}
        <div className="flex items-center gap-1">
          <span className="text-sm text-chat-text-secondary dark:text-chat-text-secondary-dark mr-2">
            {contactName || 'Contact'} is typing
          </span>
          <div className="flex items-center gap-1">
            <div 
              className="w-2 h-2 bg-chat-text-muted dark:bg-chat-text-muted-dark rounded-full animate-bounce"
              style={{ animationDelay: '0ms', animationDuration: '1.4s' }}
            />
            <div 
              className="w-2 h-2 bg-chat-text-muted dark:bg-chat-text-muted-dark rounded-full animate-bounce"
              style={{ animationDelay: '160ms', animationDuration: '1.4s' }}
            />
            <div 
              className="w-2 h-2 bg-chat-text-muted dark:bg-chat-text-muted-dark rounded-full animate-bounce"
              style={{ animationDelay: '320ms', animationDuration: '1.4s' }}
            />
          </div>
        </div>

        {/* Tail for speech bubble effect */}
        <div className={cn(
          'absolute bottom-0 left-0 w-3 h-3 -translate-x-1',
          'bg-chat-inbound dark:bg-chat-inbound-dark',
          'border-l border-b border-chat-border/20 dark:border-chat-border-dark/20'
        )}
        style={{
          clipPath: 'polygon(100% 0, 100% 100%, 0 100%)'
        }}
        />
      </div>
    </div>
  );
};

export default TypingIndicator;
