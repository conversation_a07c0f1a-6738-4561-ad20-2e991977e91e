// WhatsApp-style Sidebar Component
// Contains header, search, and chat list

import React from 'react';
import { cn } from '../../lib/utils';
import { SidebarHeader } from './SidebarHeader';
import { SearchBar } from './SearchBar';
import { ChatList } from './ChatList';

interface SidebarProps {
  className?: string;
}

export const Sidebar: React.FC<SidebarProps> = ({ className }) => {
  return (
    <div className={cn('flex flex-col h-full bg-chat-sidebar dark:bg-chat-sidebar-dark', className)}>
      {/* Header */}
      <SidebarHeader />

      {/* Search */}
      <div className="px-3 p-2">
        <SearchBar />
      </div>

      {/* Chat List */}
      <div className="flex-1 overflow-hidden">
        <ChatList />
      </div>
    </div>
  );
};

export default Sidebar;
