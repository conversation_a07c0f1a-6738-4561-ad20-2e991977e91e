// WhatsApp-style Search Bar Component
// Search input for filtering chats

import React from 'react';
import { cn } from '../../lib/utils';
import { useChatStore } from '../../stores/chatStore';
import { Search, X } from 'lucide-react';
import { But<PERSON> } from '../ui/button';

interface SearchBarProps {
  className?: string;
}

export const SearchBar: React.FC<SearchBarProps> = ({ className }) => {
  const { searchQuery, setSearchQuery } = useChatStore();

  const handleClear = () => {
    setSearchQuery('');
  };

  return (
    <div className={cn(
      'relative flex items-center',
      'bg-chat-input-bg dark:bg-chat-input-bg-dark',
      'border border-chat-input-border dark:border-chat-input-border-dark',
      'rounded-lg px-3 py-2',
      'transition-colors duration-200',
      className
    )}>
      {/* Search Icon */}
      <Search className="w-4 h-4 text-chat-text-muted dark:text-chat-text-muted-dark mr-3 flex-shrink-0" />

      {/* Search Input */}
      <input
        type="text"
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        placeholder="Search or start new chat"
        className={cn(
          'flex-1 bg-transparent text-sm',
          'text-chat-text-primary dark:text-chat-text-primary-dark',
          'placeholder:text-chat-text-muted dark:placeholder:text-chat-text-muted-dark',
          'focus:outline-none'
        )}
        aria-label="Search chats"
      />

      {/* Clear Button */}
      {searchQuery && (
        <Button
          variant="ghost"
          size="sm"
          onClick={handleClear}
          className="w-6 h-6 p-0 ml-2 text-chat-text-muted dark:text-chat-text-muted-dark hover:text-chat-text-secondary dark:hover:text-chat-text-secondary-dark"
          aria-label="Clear search"
        >
          <X className="w-4 h-4" />
        </Button>
      )}
    </div>
  );
};

export default SearchBar;
