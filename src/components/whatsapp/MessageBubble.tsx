// WhatsApp-style Message Bubble Component
// Individual message bubble with status indicators

import React from 'react';
import { cn, RTLUtils, A11yUtils } from '../../lib/utils';
import { Message } from '../../types/whatsapp-chat';
import {
  Check,
  CheckCheck,
  Clock,
  AlertCircle,
  CornerDownRight
} from 'lucide-react';

interface MessageBubbleProps {
  message: Message;
  isGrouped?: boolean;
  isLastInGroup?: boolean;
  className?: string;
}

export const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isGrouped = false,
  isLastInGroup = true,
  className
}) => {
  const messageId = A11yUtils.generateId('message');
  const isRTL = RTLUtils.isRTL(message.content);
  const textDirection = RTLUtils.getTextDirection(message.content);
  const getStatusIcon = () => {
    if (!message.isOutbound) return null;
    
    switch (message.status) {
      case 'sending':
        return <Clock className="w-3 h-3 text-chat-outbound-meta dark:text-chat-outbound-meta-dark" />;
      case 'sent':
        return <Check className="w-3 h-3 text-chat-outbound-meta dark:text-chat-outbound-meta-dark" />;
      case 'delivered':
        return <CheckCheck className="w-3 h-3 text-chat-outbound-meta dark:text-chat-outbound-meta-dark" />;
      case 'read':
        return <CheckCheck className="w-3 h-3 text-chat-read" />;
      case 'failed':
        return <AlertCircle className="w-3 h-3 text-chat-error" />;
      default:
        return null;
    }
  };

  const formatTime = (date: Date | string) => {
    // Ensure we have a proper Date object
    const dateObj = typeof date === 'string' ? new Date(date) : date;

    // Check if the date is valid
    if (!dateObj || isNaN(dateObj.getTime())) {
      return '';
    }

    return dateObj.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: false
    });
  };

  const getBubbleStyles = () => {
    const baseStyles = cn(
      'relative px-3 py-2 max-w-full break-words shadow-sm',
      'transition-colors duration-200'
    );

    if (message.isOutbound) {
      return cn(
        baseStyles,
        'bg-chat-outbound dark:bg-chat-outbound-dark',
        'text-chat-outbound-text dark:text-chat-outbound-text-dark',
        // Rounded corners - more rounded on left, sharp on bottom-right for last in group
        isLastInGroup ? 'rounded-lg rounded-br-sm' : 'rounded-lg',
        isGrouped && 'rounded-tr-sm'
      );
    } else {
      return cn(
        baseStyles,
        'bg-chat-inbound dark:bg-chat-inbound-dark',
        'text-chat-inbound-text dark:text-chat-inbound-text-dark',
        'border border-chat-border/20 dark:border-chat-border-dark/20',
        // Rounded corners - more rounded on right, sharp on bottom-left for last in group
        isLastInGroup ? 'rounded-lg rounded-bl-sm' : 'rounded-lg',
        isGrouped && 'rounded-tl-sm'
      );
    }
  };

  return (
    <div
      className={cn('group', className)}
      role="article"
      aria-labelledby={messageId}
    >
      {/* Reply indicator */}
      {message.replyTo && (
        <div className={cn(
          'flex items-center gap-1 mb-1 text-xs',
          'text-chat-text-muted dark:text-chat-text-muted-dark'
        )}>
          <CornerDownRight className="w-3 h-3" aria-hidden="true" />
          <span>Replying to message</span>
        </div>
      )}

      {/* Message bubble */}
      <div className={getBubbleStyles()}>
        {/* Forwarded indicator */}
        {message.isForwarded && (
          <div className={cn(
            'flex items-center gap-1 mb-1 text-xs font-medium',
            'text-chat-text-muted dark:text-chat-text-muted-dark'
          )}>
            <CornerDownRight className="w-3 h-3" />
            <span>Forwarded</span>
          </div>
        )}

        {/* Message content */}
        <div className="mb-1">
          {message.type === 'text' ? (
            <p
              id={messageId}
              className="text-sm leading-relaxed whitespace-pre-wrap"
              dir={textDirection}
              style={{ textAlign: RTLUtils.getTextAlign(message.content, message.isOutbound) }}
              aria-label={`${message.isOutbound ? 'Sent' : 'Received'} message: ${message.content}`}
            >
              {message.content}
            </p>
          ) : (
            <div className="text-sm">
              <span className="italic" aria-label={`${message.type} attachment`}>
                {message.type === 'image' && '📷 Photo'}
                {message.type === 'document' && '📄 Document'}
              </span>
              {message.content && (
                <p
                  className="mt-1 leading-relaxed whitespace-pre-wrap"
                  dir={textDirection}
                  style={{ textAlign: RTLUtils.getTextAlign(message.content, message.isOutbound) }}
                >
                  {message.content}
                </p>
              )}
            </div>
          )}
        </div>

        {/* Timestamp and status */}
        <div className={cn(
          'flex items-center justify-end gap-1',
          'text-xs leading-none',
          message.isOutbound
            ? 'text-chat-outbound-meta dark:text-chat-outbound-meta-dark'
            : 'text-chat-inbound-meta dark:text-chat-inbound-meta-dark'
        )}>
          {/* Star indicator */}
          {message.isStarred && (
            <span className="text-yellow-500" aria-label="Starred message">⭐</span>
          )}

          {/* Timestamp */}
          <span
            className="select-none"
            aria-label={A11yUtils.formatTimeForScreenReader(message.timestamp)}
          >
            {formatTime(message.timestamp)}
          </span>

          {/* Status indicator for outbound messages */}
          {message.isOutbound && (
            <span aria-label={A11yUtils.getMessageStatusDescription(message.status)}>
              {getStatusIcon()}
            </span>
          )}
        </div>

        {/* Tail for speech bubble effect */}
        {isLastInGroup && (
          <div className={cn(
            'absolute bottom-0 w-3 h-3',
            message.isOutbound
              ? 'right-0 translate-x-1 bg-chat-outbound dark:bg-chat-outbound-dark'
              : 'left-0 -translate-x-1 bg-chat-inbound dark:bg-chat-inbound-dark border-l border-b border-chat-border/20 dark:border-chat-border-dark/20',
            'clip-path-triangle'
          )}
          style={{
            clipPath: message.isOutbound
              ? 'polygon(0 0, 100% 0, 0 100%)'
              : 'polygon(100% 0, 100% 100%, 0 100%)'
          }}
          />
        )}
      </div>
    </div>
  );
};

export default MessageBubble;
