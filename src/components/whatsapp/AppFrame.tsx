// WhatsApp-style App Frame Component
// Main layout container with sidebar and chat area

import React, { useEffect } from 'react';
import { cn } from '../../lib/utils';
import { useChatStore } from '../../stores/chatStore';
import { Sidebar } from './Sidebar';
import { ChatArea } from './ChatArea';
import { useTheme } from '../ui/theme-provider';

interface AppFrameProps {
  className?: string;
}

export const AppFrame: React.FC<AppFrameProps> = ({ className }) => {
  const {
    currentChatId,
    loadChats,
    setTheme,
    subscribeToMessages,
    unsubscribeFromMessages
  } = useChatStore();
  const { theme: systemTheme } = useTheme();

  // Load chats and setup subscriptions on mount
  useEffect(() => {
    loadChats();
    subscribeToMessages();

    // Cleanup subscriptions on unmount
    return () => {
      unsubscribeFromMessages();
    };
  }, [loadChats, subscribeToMessages, unsubscribeFromMessages]);

  // Sync theme with system theme
  useEffect(() => {
    if (systemTheme !== 'system') {
      setTheme(systemTheme as 'light' | 'dark');
    }
  }, [systemTheme, setTheme]);

  // Detect RTL from browser language
  const isRTL = document.documentElement.dir === 'rtl' ||
    ['ar', 'he', 'fa', 'ur'].includes(navigator.language.split('-')[0]);

  return (
    <div
      className={cn(
        'h-screen flex bg-chat-bg dark:bg-chat-bg-dark overflow-hidden',
        'transition-colors duration-200',
        className
      )}
      dir={isRTL ? 'rtl' : 'ltr'}
      role="application"
      aria-label="WhatsApp Web Demo"
    >
      {/* Sidebar */}
      <div
        className={cn(
          'flex-shrink-0 border-r border-chat-border dark:border-chat-border-dark',
          'bg-chat-sidebar dark:bg-chat-sidebar-dark',
          // Desktop: Always visible, fixed width
          'md:flex md:w-80 lg:w-96',
          // Mobile: Show when no chat selected, hide when chat is open
          !currentChatId ? 'flex w-full' : 'hidden md:flex'
        )}
      >
        <Sidebar className="w-full" />
      </div>

      {/* Chat Area */}
      <div
        className={cn(
          'flex-1 flex flex-col',
          'bg-chat-bg dark:bg-chat-bg-dark',
          // Mobile: Show when chat is selected, hidden when no chat
          currentChatId ? 'flex' : 'hidden md:flex'
        )}
      >
        {currentChatId ? (
          <ChatArea />
        ) : (
          <WelcomeScreen />
        )}
      </div>
    </div>
  );
};

// Welcome screen when no chat is selected
const WelcomeScreen: React.FC = () => {
  return (
    <div className="flex-1 flex items-center justify-center bg-chat-bg dark:bg-chat-bg-dark">
      <div className="text-center p-8 max-w-md">
        <div className="w-24 h-24 mx-auto mb-6 bg-chat-gray-100 dark:bg-chat-gray-800 rounded-full flex items-center justify-center">
          <svg
            className="w-12 h-12 text-chat-text-muted dark:text-chat-text-muted-dark"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
            />
          </svg>
        </div>
        <h3 className="text-xl font-semibold text-chat-text-primary dark:text-chat-text-primary-dark mb-2">
          WhatsApp Web
        </h3>
        <p className="text-chat-text-secondary dark:text-chat-text-secondary-dark text-sm leading-relaxed">
          Send and receive messages without keeping your phone online.
          <br />
          Use WhatsApp on up to 4 linked devices and 1 phone at the same time.
        </p>
        <div className="mt-8 p-4 bg-chat-inbound dark:bg-chat-inbound-dark rounded-lg border border-chat-border dark:border-chat-border-dark">
          <div className="flex items-center gap-3 text-sm text-chat-text-secondary dark:text-chat-text-secondary-dark">
            <svg className="w-5 h-5 text-chat-primary dark:text-chat-primary-light" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
            <span>This is a demo interface. Select a chat to start messaging.</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AppFrame;
