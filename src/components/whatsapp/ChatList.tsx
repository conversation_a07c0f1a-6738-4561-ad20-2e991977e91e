// WhatsApp-style Chat List Component
// Displays filtered list of chats with virtual scrolling for performance

import React from 'react';
import { cn } from '../../lib/utils';
import { useChatStore, useFilteredChats } from '../../stores/chatStore';
import { ChatItem } from './ChatItem';

interface ChatListProps {
  className?: string;
}

export const ChatList: React.FC<ChatListProps> = ({ className }) => {
  const { currentChatId, selectChat, loading, error } = useChatStore();
  const filteredChats = useFilteredChats();

  if (loading) {
    return (
      <div className={cn('flex items-center justify-center h-32', className)}>
        <div className="flex items-center gap-2 text-chat-text-muted dark:text-chat-text-muted-dark">
          <div className="w-4 h-4 border-2 border-chat-primary border-t-transparent rounded-full animate-spin" />
          <span className="text-sm">Loading chats...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn('flex items-center justify-center h-32 px-4', className)}>
        <div className="text-center">
          <p className="text-sm text-red-500 mb-2">Failed to load chats</p>
          <p className="text-xs text-chat-text-muted dark:text-chat-text-muted-dark">{error}</p>
        </div>
      </div>
    );
  }

  if (filteredChats.length === 0) {
    return (
      <div className={cn('flex items-center justify-center h-32 px-4', className)}>
        <div className="text-center">
          <p className="text-sm text-chat-text-muted dark:text-chat-text-muted-dark mb-1">
            No chats found
          </p>
          <p className="text-xs text-chat-text-muted dark:text-chat-text-muted-dark">
            Start a new conversation or adjust your search
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('h-full overflow-y-auto', className)}>
      <div className="py-1">
        {filteredChats.map((chat) => (
          <ChatItem
            key={chat.id}
            chat={chat}
            isSelected={chat.id === currentChatId}
            onClick={() => selectChat(chat.id)}
          />
        ))}
      </div>
    </div>
  );
};

export default ChatList;
