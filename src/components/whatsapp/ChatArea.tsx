// WhatsApp-style Chat Area Component
// Contains chat header, message list, and composer

import React from 'react';
import { cn } from '../../lib/utils';
import { ChatHeader } from './ChatHeader';
import { MessageList } from './MessageList';
import { Composer } from './Composer';
import { useChatStore } from '../../stores/chatStore';

interface ChatAreaProps {
  className?: string;
}

export const ChatArea: React.FC<ChatAreaProps> = ({ className }) => {
  const { currentChatId } = useChatStore();

  if (!currentChatId) {
    return null;
  }

  return (
    <div className={cn('flex flex-col h-full', className)}>
      {/* Chat Header */}
      <ChatHeader />

      {/* Messages Area */}
      <div className="flex-1 overflow-hidden relative">
        {/* WhatsApp background pattern */}
        <div 
          className="absolute inset-0 opacity-[0.06] dark:opacity-[0.03]"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}
        />
        
        {/* Message List */}
        <MessageList />
      </div>

      {/* Composer */}
      <Composer />
    </div>
  );
};

export default ChatArea;
