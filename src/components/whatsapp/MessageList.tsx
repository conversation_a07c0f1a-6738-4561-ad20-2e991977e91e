// WhatsApp-style Message List Component
// Displays messages with auto-scroll and typing indicator

import React, { useEffect, useRef } from 'react';
import { cn } from '../../lib/utils';
import { useChatStore, useCurrentMessages, useCurrentChat, useIsTyping } from '../../stores/chatStore';
import { MessageBubble } from './MessageBubble';
import { VoiceBubble } from './VoiceBubble';
import { TypingIndicator } from './TypingIndicator';

interface MessageListProps {
  className?: string;
}

export const MessageList: React.FC<MessageListProps> = ({ className }) => {
  const messages = useCurrentMessages();
  const currentChat = useCurrentChat();
  const isTyping = useIsTyping(currentChat?.id || '');
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, isTyping]);

  // Group messages by date
  const groupMessagesByDate = () => {
    const groups: { date: string; messages: typeof messages }[] = [];
    let currentDate = '';
    let currentGroup: typeof messages = [];

    messages.forEach((message) => {
      const messageDate = message.timestamp.toDateString();
      
      if (messageDate !== currentDate) {
        if (currentGroup.length > 0) {
          groups.push({ date: currentDate, messages: currentGroup });
        }
        currentDate = messageDate;
        currentGroup = [message];
      } else {
        currentGroup.push(message);
      }
    });

    if (currentGroup.length > 0) {
      groups.push({ date: currentDate, messages: currentGroup });
    }

    return groups;
  };

  const formatDateHeader = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString('en-US', { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      });
    }
  };

  const messageGroups = groupMessagesByDate();

  if (messages.length === 0) {
    return (
      <div className={cn(
        'flex-1 flex items-center justify-center p-8',
        className
      )}>
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-chat-gray-100 dark:bg-chat-gray-800 rounded-full flex items-center justify-center">
            <svg
              className="w-8 h-8 text-chat-text-muted dark:text-chat-text-muted-dark"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
              />
            </svg>
          </div>
          <p className="text-chat-text-muted dark:text-chat-text-muted-dark text-sm">
            No messages yet. Start the conversation!
          </p>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={scrollAreaRef}
      className={cn('flex-1 px-4 py-2 overflow-y-auto', className)}
    >
      <div className="space-y-1">
        {messageGroups.map((group, groupIndex) => (
          <div key={groupIndex}>
            {/* Date header */}
            <div className="flex justify-center my-4">
              <div className="bg-chat-inbound dark:bg-chat-inbound-dark px-3 py-1 rounded-lg shadow-sm">
                <span className="text-xs text-chat-text-secondary dark:text-chat-text-secondary-dark font-medium">
                  {formatDateHeader(group.date)}
                </span>
              </div>
            </div>

            {/* Messages for this date */}
            {group.messages.map((message, messageIndex) => {
              const prevMessage = messageIndex > 0 ? group.messages[messageIndex - 1] : null;
              const nextMessage = messageIndex < group.messages.length - 1 ? group.messages[messageIndex + 1] : null;
              
              // Check if this message should be grouped with the previous one
              const isGrouped = prevMessage && 
                prevMessage.isOutbound === message.isOutbound &&
                (message.timestamp.getTime() - prevMessage.timestamp.getTime()) < 60000; // 1 minute

              // Check if this is the last message in a group
              const isLastInGroup = !nextMessage || 
                nextMessage.isOutbound !== message.isOutbound ||
                (nextMessage.timestamp.getTime() - message.timestamp.getTime()) >= 60000;

              return (
                <div
                  key={message.id}
                  className={cn(
                    'flex',
                    message.isOutbound ? 'justify-end' : 'justify-start',
                    !isGrouped && 'mt-2'
                  )}
                >
                  <div className={cn(
                    'max-w-[70%] sm:max-w-[60%]',
                    message.isOutbound ? 'ml-auto' : 'mr-auto'
                  )}>
                    {message.type === 'voice' ? (
                      <VoiceBubble
                        message={message}
                        isGrouped={isGrouped}
                        isLastInGroup={isLastInGroup}
                      />
                    ) : (
                      <MessageBubble
                        message={message}
                        isGrouped={isGrouped}
                        isLastInGroup={isLastInGroup}
                      />
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        ))}

        {/* Typing indicator */}
        {isTyping && (
          <div className="flex justify-start mt-2">
            <div className="max-w-[70%] sm:max-w-[60%] mr-auto">
              <TypingIndicator contactName={currentChat?.name} />
            </div>
          </div>
        )}

        {/* Scroll anchor */}
        <div ref={messagesEndRef} />
      </div>
    </div>
  );
};

export default MessageList;
