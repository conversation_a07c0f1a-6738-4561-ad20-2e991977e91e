// WhatsApp-style Composer Component
// Message input with emoji, attachments, voice recording, and send

import React, { useState, useRef, useEffect } from 'react';
import { cn } from '../../lib/utils';
import { useChatStore } from '../../stores/chatStore';
import { Button } from '../ui/button';
import { 
  Smile, 
  Paperclip, 
  Mic, 
  Send, 
  X,
  Square
} from 'lucide-react';

interface ComposerProps {
  className?: string;
}

export const Composer: React.FC<ComposerProps> = ({ className }) => {
  const { 
    currentChatId, 
    sendTextMessage, 
    sendVoiceMessage, 
    setTyping,
    isRecording,
    startRecording,
    stopRecording
  } = useChatStore();
  
  const [message, setMessage] = useState('');
  const [isRecordingVoice, setIsRecordingVoice] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const recordingTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
    }
  }, [message]);

  // Typing indicator
  useEffect(() => {
    if (!currentChatId) return;

    const isTyping = message.trim().length > 0;
    setTyping(currentChatId, isTyping);

    // Clear typing when component unmounts or message is sent
    return () => {
      setTyping(currentChatId, false);
    };
  }, [message, currentChatId, setTyping]);

  const handleSendMessage = async () => {
    if (!currentChatId || !message.trim()) return;

    const messageText = message.trim();
    setMessage('');
    
    try {
      await sendTextMessage(currentChatId, messageText);
    } catch (error) {
      console.error('Failed to send message:', error);
      // Restore message on error
      setMessage(messageText);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const startVoiceRecording = async () => {
    if (!currentChatId) return;

    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];
      
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        
        if (recordingDuration > 0.5) { // Only send if recording is longer than 0.5 seconds
          try {
            await sendVoiceMessage(currentChatId, audioBlob, recordingDuration);
          } catch (error) {
            console.error('Failed to send voice message:', error);
          }
        }
        
        // Clean up
        stream.getTracks().forEach(track => track.stop());
        setIsRecordingVoice(false);
        setRecordingDuration(0);
        stopRecording();
        
        if (recordingTimerRef.current) {
          clearInterval(recordingTimerRef.current);
          recordingTimerRef.current = null;
        }
      };

      mediaRecorder.start();
      setIsRecordingVoice(true);
      startRecording(currentChatId);
      
      // Start timer
      const startTime = Date.now();
      recordingTimerRef.current = setInterval(() => {
        setRecordingDuration((Date.now() - startTime) / 1000);
      }, 100);
      
    } catch (error) {
      console.error('Failed to start recording:', error);
    }
  };

  const stopVoiceRecording = () => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
      mediaRecorderRef.current.stop();
    }
  };

  const cancelVoiceRecording = () => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
      mediaRecorderRef.current.stop();
      // Clear the audio chunks to prevent sending
      audioChunksRef.current = [];
    }
    
    setIsRecordingVoice(false);
    setRecordingDuration(0);
    stopRecording();
    
    if (recordingTimerRef.current) {
      clearInterval(recordingTimerRef.current);
      recordingTimerRef.current = null;
    }
  };

  const formatRecordingTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (!currentChatId) return null;

  return (
    <div className={cn(
      'flex items-end gap-2 p-4',
      'bg-chat-header dark:bg-chat-header-dark',
      'border-t border-chat-border dark:border-chat-border-dark',
      className
    )}>
      {isRecordingVoice ? (
        // Voice recording UI
        <div className="flex-1 flex items-center gap-3 bg-chat-input-bg dark:bg-chat-input-bg-dark rounded-full px-4 py-3 border border-chat-input-border dark:border-chat-input-border-dark">
          <div className="flex items-center gap-2 flex-1">
            <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse" />
            <span className="text-sm text-chat-text-primary dark:text-chat-text-primary-dark">
              Recording...
            </span>
            <span className="text-sm font-mono text-chat-text-secondary dark:text-chat-text-secondary-dark">
              {formatRecordingTime(recordingDuration)}
            </span>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={cancelVoiceRecording}
              className="w-8 h-8 p-0 text-chat-text-secondary dark:text-chat-text-secondary-dark hover:bg-chat-hover dark:hover:bg-chat-hover-dark rounded-full"
              title="Cancel recording"
            >
              <X className="w-4 h-4" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={stopVoiceRecording}
              className="w-8 h-8 p-0 text-chat-primary dark:text-chat-primary-light hover:bg-chat-primary/10 dark:hover:bg-chat-primary-light/10 rounded-full"
              title="Send voice message"
            >
              <Send className="w-4 h-4" />
            </Button>
          </div>
        </div>
      ) : (
        // Normal composer UI
        <>
          {/* Emoji button */}
          <Button
            variant="ghost"
            size="sm"
            className="w-10 h-10 p-0 text-chat-text-secondary dark:text-chat-text-secondary-dark hover:bg-chat-hover dark:hover:bg-chat-hover-dark rounded-full flex-shrink-0"
            title="Emoji"
          >
            <Smile className="w-5 h-5" />
          </Button>

          {/* Text input container */}
          <div className="flex-1 relative">
            <div className="flex items-end bg-chat-input-bg dark:bg-chat-input-bg-dark rounded-3xl border border-chat-input-border dark:border-chat-input-border-dark">
              <textarea
                ref={textareaRef}
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyDown={handleKeyPress}
                placeholder="Type a message"
                className={cn(
                  'flex-1 bg-transparent px-4 py-3 pr-12 text-sm resize-none',
                  'text-chat-text-primary dark:text-chat-text-primary-dark',
                  'placeholder:text-chat-text-muted dark:placeholder:text-chat-text-muted-dark',
                  'focus:outline-none',
                  'max-h-[120px] min-h-[44px]'
                )}
                rows={1}
                style={{ lineHeight: '1.4' }}
              />
              
              {/* Attachment button */}
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-2 bottom-2 w-8 h-8 p-0 text-chat-text-secondary dark:text-chat-text-secondary-dark hover:bg-chat-hover dark:hover:bg-chat-hover-dark rounded-full"
                title="Attach"
              >
                <Paperclip className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Send or Voice button */}
          {message.trim() ? (
            <Button
              onClick={handleSendMessage}
              size="sm"
              className="w-10 h-10 p-0 bg-chat-primary hover:bg-chat-primary-dark text-chat-text-on-primary rounded-full flex-shrink-0"
              title="Send message"
            >
              <Send className="w-5 h-5" />
            </Button>
          ) : (
            <Button
              onMouseDown={startVoiceRecording}
              size="sm"
              className="w-10 h-10 p-0 bg-chat-primary hover:bg-chat-primary-dark text-chat-text-on-primary rounded-full flex-shrink-0"
              title="Hold to record voice message"
            >
              <Mic className="w-5 h-5" />
            </Button>
          )}
        </>
      )}
    </div>
  );
};

export default Composer;
