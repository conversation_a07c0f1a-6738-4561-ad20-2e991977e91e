// WhatsApp-style Voice Message Bubble Component
// Voice message with waveform, play/pause controls, and duration

import React, { useState, useRef, useEffect } from 'react';
import { cn, A11yUtils } from '../../lib/utils';
import { Message } from '../../types/whatsapp-chat';
import { 
  Play, 
  Pause, 
  Check, 
  CheckCheck, 
  Clock, 
  AlertCircle,
  Mic
} from 'lucide-react';
import { Button } from '../ui/button';

interface VoiceBubbleProps {
  message: Message;
  isGrouped?: boolean;
  isLastInGroup?: boolean;
  className?: string;
}

export const VoiceBubble: React.FC<VoiceBubbleProps> = ({ 
  message, 
  isGrouped = false,
  isLastInGroup = true,
  className 
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(message.voiceData?.duration || 0);
  const audioRef = useRef<HTMLAudioElement>(null);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleTimeUpdate = () => {
      setCurrentTime(audio.currentTime);
    };

    const handleLoadedMetadata = () => {
      setDuration(audio.duration);
    };

    const handleEnded = () => {
      setIsPlaying(false);
      setCurrentTime(0);
    };

    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('ended', handleEnded);

    return () => {
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('ended', handleEnded);
    };
  }, []);

  const handlePlayPause = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
      setIsPlaying(false);
    } else {
      audio.play();
      setIsPlaying(true);
    }
  };

  const getStatusIcon = () => {
    if (!message.isOutbound) return null;
    
    switch (message.status) {
      case 'sending':
        return <Clock className="w-3 h-3 text-chat-outbound-meta dark:text-chat-outbound-meta-dark" />;
      case 'sent':
        return <Check className="w-3 h-3 text-chat-outbound-meta dark:text-chat-outbound-meta-dark" />;
      case 'delivered':
        return <CheckCheck className="w-3 h-3 text-chat-outbound-meta dark:text-chat-outbound-meta-dark" />;
      case 'read':
        return <CheckCheck className="w-3 h-3 text-chat-read" />;
      case 'failed':
        return <AlertCircle className="w-3 h-3 text-chat-error" />;
      default:
        return null;
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatTimestamp = (date: Date | string) => {
    // Ensure we have a proper Date object
    const dateObj = typeof date === 'string' ? new Date(date) : date;

    // Check if the date is valid
    if (!dateObj || isNaN(dateObj.getTime())) {
      return '';
    }

    return dateObj.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: false
    });
  };

  const getBubbleStyles = () => {
    const baseStyles = cn(
      'relative px-3 py-2 max-w-full shadow-sm',
      'transition-colors duration-200'
    );

    if (message.isOutbound) {
      return cn(
        baseStyles,
        'bg-chat-outbound dark:bg-chat-outbound-dark',
        'text-chat-outbound-text dark:text-chat-outbound-text-dark',
        isLastInGroup ? 'rounded-lg rounded-br-sm' : 'rounded-lg',
        isGrouped && 'rounded-tr-sm'
      );
    } else {
      return cn(
        baseStyles,
        'bg-chat-inbound dark:bg-chat-inbound-dark',
        'text-chat-inbound-text dark:text-chat-inbound-text-dark',
        'border border-chat-border/20 dark:border-chat-border-dark/20',
        isLastInGroup ? 'rounded-lg rounded-bl-sm' : 'rounded-lg',
        isGrouped && 'rounded-tl-sm'
      );
    }
  };

  // Generate waveform visualization
  const renderWaveform = () => {
    const waveform = message.voiceData?.waveform || Array.from({ length: 20 }, () => Math.random());
    const progress = duration > 0 ? currentTime / duration : 0;
    
    return (
      <div className="flex items-center gap-0.5 flex-1 mx-2">
        {waveform.map((amplitude, index) => {
          const isActive = index / waveform.length <= progress;
          const height = Math.max(2, amplitude * 16); // Min height 2px, max 16px
          
          return (
            <div
              key={index}
              className={cn(
                'w-0.5 rounded-full transition-colors duration-150',
                isActive 
                  ? message.isOutbound 
                    ? 'bg-chat-outbound-text dark:bg-chat-outbound-text-dark' 
                    : 'bg-chat-primary dark:bg-chat-primary-light'
                  : message.isOutbound
                    ? 'bg-chat-outbound-meta dark:bg-chat-outbound-meta-dark'
                    : 'bg-chat-inbound-meta dark:bg-chat-inbound-meta-dark'
              )}
              style={{ height: `${height}px` }}
            />
          );
        })}
      </div>
    );
  };

  const voiceMessageId = A11yUtils.generateId('voice-message');
  const voiceDescription = A11yUtils.getVoiceMessageDescription(duration, message.isOutbound);

  return (
    <div
      className={cn('group', className)}
      role="article"
      aria-labelledby={voiceMessageId}
    >
      <div className={getBubbleStyles()}>
        {/* Audio element */}
        {message.voiceData?.audioUrl && (
          <audio
            ref={audioRef}
            src={message.voiceData.audioUrl}
            preload="metadata"
            aria-hidden="true"
          />
        )}

        {/* Voice message content */}
        <div
          className="flex items-center gap-2 min-w-[200px]"
          id={voiceMessageId}
          aria-label={voiceDescription}
        >
          {/* Play/Pause button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handlePlayPause}
            className={cn(
              'w-8 h-8 p-0 rounded-full flex-shrink-0',
              message.isOutbound
                ? 'hover:bg-chat-outbound-text/10 dark:hover:bg-chat-outbound-text-dark/10'
                : 'hover:bg-chat-primary/10 dark:hover:bg-chat-primary-light/10'
            )}
            disabled={!message.voiceData?.audioUrl}
            aria-label={isPlaying ? 'Pause voice message' : 'Play voice message'}
          >
            {isPlaying ? (
              <Pause className={cn(
                'w-4 h-4',
                message.isOutbound 
                  ? 'text-chat-outbound-text dark:text-chat-outbound-text-dark' 
                  : 'text-chat-primary dark:text-chat-primary-light'
              )} />
            ) : (
              <Play className={cn(
                'w-4 h-4',
                message.isOutbound 
                  ? 'text-chat-outbound-text dark:text-chat-outbound-text-dark' 
                  : 'text-chat-primary dark:text-chat-primary-light'
              )} />
            )}
          </Button>

          {/* Waveform */}
          {renderWaveform()}

          {/* Duration */}
          <span className={cn(
            'text-xs font-mono flex-shrink-0',
            message.isOutbound 
              ? 'text-chat-outbound-meta dark:text-chat-outbound-meta-dark' 
              : 'text-chat-inbound-meta dark:text-chat-inbound-meta-dark'
          )}>
            {formatTime(isPlaying ? currentTime : duration)}
          </span>
        </div>

        {/* Timestamp and status */}
        <div className={cn(
          'flex items-center justify-end gap-1 mt-1',
          'text-xs leading-none',
          message.isOutbound 
            ? 'text-chat-outbound-meta dark:text-chat-outbound-meta-dark' 
            : 'text-chat-inbound-meta dark:text-chat-inbound-meta-dark'
        )}>
          <span className="select-none">
            {formatTimestamp(message.timestamp)}
          </span>
          {getStatusIcon()}
        </div>

        {/* Tail for speech bubble effect */}
        {isLastInGroup && (
          <div className={cn(
            'absolute bottom-0 w-3 h-3',
            message.isOutbound
              ? 'right-0 translate-x-1 bg-chat-outbound dark:bg-chat-outbound-dark'
              : 'left-0 -translate-x-1 bg-chat-inbound dark:bg-chat-inbound-dark border-l border-b border-chat-border/20 dark:border-chat-border-dark/20'
          )}
          style={{
            clipPath: message.isOutbound
              ? 'polygon(0 0, 100% 0, 0 100%)'
              : 'polygon(100% 0, 100% 100%, 0 100%)'
          }}
          />
        )}
      </div>
    </div>
  );
};

export default VoiceBubble;
