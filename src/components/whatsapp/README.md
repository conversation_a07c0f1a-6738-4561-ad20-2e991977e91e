# WhatsApp Business Manager - Enterprise Implementation

## Overview

Production-ready WhatsApp Business API management system using AWS Amplify, AppSync GraphQL, and Meta's official Embedded Signup. This implementation provides a complete manager interface with analytics, number management, and streamlined onboarding.

## Components Architecture

### 1. WhatsAppManager.tsx
**Main dashboard component with full number management:**

- **Analytics Dashboard**: Real-time metrics and performance indicators
- **Number Management**: List, edit, delete WhatsApp Business numbers  
- **Add Number**: Triggers the onboarding flow
- **Store Connections**: View and manage Shopify store associations

**Features:**
- Real-time analytics (total messages, response rates, health status)
- Number cards with performance metrics
- CRUD operations for WhatsApp numbers
- Store connection management
- Responsive design for mobile/desktop

### 2. WhatsAppOnboarding.tsx
**Simplified onboarding flow using Meta's official implementation:**

**Steps:**
1. **Start**: Button to begin process
2. **Meta Signup**: Opens Meta's embedded signup popup
3. **Processing**: Backend Lambda processes via AppSync
4. **Store Selection**: Connect to Shopify stores
5. **Complete**: Setup finished, return to manager

**Meta Integration:**
```typescript
// Uses Meta's official embedded signup
window.FB.login(fbLoginCallback, {
  config_id: '***************', // Your config ID
  response_type: 'code',
  override_default_response_type: true,
  extras: {
    setup: {}
  }
});
```

## GraphQL Operations

### Queries
- `GET_WHATSAPP_NUMBERS`: Fetch all user's WhatsApp numbers
- `GET_WHATSAPP_NUMBER`: Get specific number details
- `GET_WHATSAPP_ANALYTICS`: Dashboard analytics data

### Mutations
- `PROCESS_WHATSAPP_SIGNUP`: Process Meta's embedded signup response
- `UPDATE_WHATSAPP_NUMBER`: Update number settings/status
- `DELETE_WHATSAPP_NUMBER`: Remove WhatsApp number
- `CONNECT_STORE_TO_WHATSAPP`: Associate with Shopify stores

## Implementation Flow

### 1. User Experience
```
WhatsApp Manager → Add Number → Meta Signup → Processing → Store Connection → Complete
```

### 2. Technical Flow
```typescript
// 1. User clicks "Add Number" in manager
<WhatsAppManager showOnboarding={true} />

// 2. Meta embedded signup
window.FB.login(callback, { config_id: '***************' });

// 3. Process response via AppSync
const { data } = await processWhatsAppSignup({
  variables: { input: { code, state, wabaId } }
});

// 4. Update UI with real-time data
// AppSync subscriptions automatically update the manager
```

### 3. Backend Processing
```python
# Lambda processes Meta's response
def lambda_handler(event, context):
    # 1. Exchange code for token
    # 2. Subscribe to WABA 
    # 3. Register phone number
    # 4. Setup webhooks with callback URL
    # 5. Save to DynamoDB
    # 6. Return success response
```

## Meta's Official Implementation

### Config ID Setup
- **Production Config**: `***************`
- **App ID**: Your Facebook App ID
- **Callback URL**: `https://apis.notefy.app/webhooks/whatsapp/{businessAccountId}`

### Required Meta Setup
1. Facebook App with WhatsApp Business API permissions
2. App review for `whatsapp_business_messaging`
3. Embedded signup configuration in App Dashboard
4. Webhook verification endpoint

## AWS Infrastructure Integration

### AppSync Schema Types
```graphql
type WhatsAppNumber {
  id: ID!
  phoneNumber: String!
  displayName: String!
  status: WhatsAppStatus!
  businessAccountId: String!
  totalMessages: Int!
  monthlyMessages: Int!
  responseRate: Float!
  connectedStores: [String!]!
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

input ProcessWhatsAppSignupInput {
  code: String!
  state: String!
  wabaId: String!
}
```

### Lambda Integration
- **Function**: `notefy-dev-whatsapp-signup-processor`
- **Trigger**: AppSync GraphQL mutation
- **Processing**: Meta API calls, DynamoDB storage, webhook setup
- **Response**: Real-time updates via AppSync subscriptions

### DynamoDB Tables
- **whatsapp_numbers**: Store WhatsApp Business number data
- **whatsapp_webhooks**: Track webhook configurations
- **store_connections**: Shopify-WhatsApp associations

## Webhook Architecture

### Callback URL Structure
```
https://apis.notefy.app/webhooks/whatsapp/{businessAccountId}?business_account={id}
```

### ALB Routing
- **Target**: WhatsApp webhook processor Lambda
- **Authentication**: Business Account ID validation
- **Processing**: Route to appropriate customer's processing pipeline

## Production Deployment

### Frontend Deployment
1. Build with `yarn build`
2. Deploy to S3 + CloudFront
3. Environment variables configured via Amplify

### Backend Deployment  
1. Lambda functions via GitHub Actions
2. AppSync schema deployment
3. DynamoDB table creation via Terraform

### Meta Configuration
1. Add production callback URLs to App Dashboard
2. Submit App Review for WhatsApp Business permissions
3. Configure webhook endpoints and verification

## Development Notes

### Local Development
```bash
# Install dependencies
yarn install

# Start development server
yarn dev

# GraphQL codegen (if using)
yarn codegen
```

### Environment Variables
```typescript
// Set in Amplify environment
const config = {
  facebookAppId: process.env.REACT_APP_FACEBOOK_APP_ID,
  whatsappConfigId: '***************',
  apiUrl: process.env.REACT_APP_API_URL
};
```

### Testing
- Use Meta's test phone numbers for development
- Test webhook delivery with ngrok for local development
- Monitor CloudWatch logs for Lambda execution

## Security & Compliance

### Data Protection
- All tokens stored securely in AWS Secrets Manager
- Business Account ID used for multi-tenant isolation
- Webhook verification via Meta's security headers

### Access Control
- Cognito authentication required
- IAM roles for Lambda execution
- AppSync authorization rules

This implementation provides enterprise-grade WhatsApp Business management with clean separation of concerns, scalable architecture, and Meta's best practices compliance. 