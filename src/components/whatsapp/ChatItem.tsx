// WhatsApp-style Chat Item Component
// Individual chat item in the chat list

import React from 'react';
import { cn, RTLUtils, A11yUtils } from '../../lib/utils';
import { Chat } from '../../types/whatsapp-chat';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { formatDistanceToNow } from 'date-fns';
import { 
  Volume2, 
  VolumeX, 
  Pin, 
  Check, 
  CheckCheck,
  Clock,
  AlertCircle
} from 'lucide-react';

interface ChatItemProps {
  chat: Chat;
  isSelected: boolean;
  onClick: () => void;
  className?: string;
}

export const ChatItem: React.FC<ChatItemProps> = ({ 
  chat, 
  isSelected, 
  onClick, 
  className 
}) => {
  const chatItemId = A11yUtils.generateId('chat-item');

  const getLastMessagePreview = () => {
    if (!chat.lastMessage) return 'No messages yet';

    const { content, type, isOutbound } = chat.lastMessage;

    switch (type) {
      case 'voice':
        return (
          <span className="flex items-center gap-1">
            <Volume2 className="w-3 h-3" aria-hidden="true" />
            Voice message
          </span>
        );
      case 'image':
        return (
          <span className="flex items-center gap-1">
            📷 Photo
          </span>
        );
      case 'document':
        return (
          <span className="flex items-center gap-1">
            📄 Document
          </span>
        );
      default:
        const preview = content.length > 50 ? `${content.substring(0, 50)}...` : content;
        const isRTL = RTLUtils.isRTL(preview);
        return (
          <span dir={RTLUtils.getTextDirection(preview)} style={{ textAlign: isRTL ? 'right' : 'left' }}>
            {preview}
          </span>
        );
    }
  };

  const getStatusIcon = () => {
    if (!chat.lastMessage || !chat.lastMessage.isOutbound) return null;
    
    const { status } = chat.lastMessage;
    
    switch (status) {
      case 'sending':
        return <Clock className="w-3 h-3 text-chat-text-muted dark:text-chat-text-muted-dark" />;
      case 'sent':
        return <Check className="w-3 h-3 text-chat-text-muted dark:text-chat-text-muted-dark" />;
      case 'delivered':
        return <CheckCheck className="w-3 h-3 text-chat-text-muted dark:text-chat-text-muted-dark" />;
      case 'read':
        return <CheckCheck className="w-3 h-3 text-chat-delivered dark:text-chat-delivered" />;
      case 'failed':
        return <AlertCircle className="w-3 h-3 text-chat-error" />;
      default:
        return null;
    }
  };

  const formatTimestamp = (date: Date | string) => {
    // Ensure we have a proper Date object
    const dateObj = typeof date === 'string' ? new Date(date) : date;

    // Check if the date is valid
    if (!dateObj || isNaN(dateObj.getTime())) {
      return '';
    }

    const now = new Date();
    const diffInHours = (now.getTime() - dateObj.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return dateObj.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: false
      });
    } else if (diffInHours < 168) { // 7 days
      return dateObj.toLocaleDateString('en-US', { weekday: 'short' });
    } else {
      return dateObj.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      });
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  const chatDescription = `Chat with ${chat.name}. ${chat.unreadCount > 0 ? `${chat.unreadCount} unread message${chat.unreadCount > 1 ? 's' : ''}. ` : ''}${chat.isOnline ? 'Online' : 'Offline'}. Last message: ${chat.lastMessage?.content || 'No messages yet'}`;

  return (
    <div
      onClick={onClick}
      className={cn(
        'flex items-center gap-3 p-3 cursor-pointer transition-colors duration-150',
        'hover:bg-chat-hover dark:hover:bg-chat-hover-dark',
        'border-b border-chat-border/30 dark:border-chat-border-dark/30',
        'focus:outline-none focus:ring-2 focus:ring-chat-primary focus:ring-inset',
        isSelected && 'bg-chat-hover dark:bg-chat-hover-dark',
        className
      )}
      role="button"
      tabIndex={0}
      aria-labelledby={chatItemId}
      aria-describedby={`${chatItemId}-description`}
      aria-pressed={isSelected}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onClick();
        }
      }}
    >
      {/* Screen reader description */}
      <div id={`${chatItemId}-description`} className="sr-only">
        {chatDescription}
      </div>
      {/* Avatar */}
      <div className="relative flex-shrink-0">
        <Avatar className="w-12 h-12">
          <AvatarImage src={chat.avatar} alt={chat.name} />
          <AvatarFallback className="bg-chat-primary text-chat-text-on-primary text-sm font-medium">
            {getInitials(chat.name)}
          </AvatarFallback>
        </Avatar>
        
        {/* Online indicator */}
        {chat.isOnline && (
          <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-chat-online border-2 border-chat-sidebar dark:border-chat-sidebar-dark rounded-full" />
        )}
      </div>

      {/* Chat Info */}
      <div className="flex-1 min-w-0">
        {/* Top row: Name and timestamp */}
        <div className="flex items-center justify-between mb-1">
          <div className="flex items-center gap-2 min-w-0 flex-1">
            <h3
              id={chatItemId}
              className={cn(
                'font-medium text-sm truncate',
                'text-chat-text-primary dark:text-chat-text-primary-dark'
              )}
            >
              {chat.name}
            </h3>
            
            {/* Icons */}
            <div className="flex items-center gap-1 flex-shrink-0">
              {chat.isPinned && (
                <Pin className="w-3 h-3 text-chat-text-muted dark:text-chat-text-muted-dark" />
              )}
              {chat.isMuted && (
                <VolumeX className="w-3 h-3 text-chat-text-muted dark:text-chat-text-muted-dark" />
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-1 flex-shrink-0 ml-2">
            {getStatusIcon()}
            <span className="text-xs text-chat-text-muted dark:text-chat-text-muted-dark">
              {chat.lastMessage ? formatTimestamp(chat?.lastMessage?.timestamp) : ''}
            </span>
          </div>
        </div>

        {/* Bottom row: Last message and unread count */}
        <div className="flex items-center justify-between">
          <p className={cn(
            'text-sm truncate flex-1',
            'text-chat-text-secondary dark:text-chat-text-secondary-dark'
          )}>
            {getLastMessagePreview()}
          </p>
          
          {/* Unread count */}
          {chat.unreadCount > 0 && (
            <div className="ml-2 flex-shrink-0">
              <span className={cn(
                'inline-flex items-center justify-center',
                'min-w-[20px] h-5 px-1.5 rounded-full text-xs font-medium',
                'bg-chat-primary text-chat-text-on-primary',
                chat.isMuted && 'bg-chat-text-muted dark:bg-chat-text-muted-dark'
              )}>
                {chat.unreadCount > 99 ? '99+' : chat.unreadCount}
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatItem;
