import React, { useState, useMemo, useCallback, useEffect } from "react"
import { Refresh<PERSON>w, AlertCircle, Loader2 } from "lucide-react"
import { But<PERSON> } from "../ui/button"
import { cn } from "../../lib/utils"
import { useConversations } from "../../hooks/useConversations"
import ConversationItem from "./ConversationItem"
import type { ConversationListProps } from "../../types/chat"

const ConversationList: React.FC<ConversationListProps> = ({
  onConversationSelect,
  selectedConversationId,
  unreadNotifications = [],
  className,
  searchQuery = "",
  activeFilters = {}
}) => {
  const [isRefreshing, setIsRefreshing] = useState(false)

  // Fetch conversations using our custom hook
  const { conversations, loading, error, refetch, clearUnreadCount } =
    useConversations(
      Object.keys(activeFilters).length > 0 ? activeFilters : undefined
    )

  // Filter conversations by search query (client-side search)
  const filteredConversations = useMemo(() => {
    if (!searchQuery.trim()) return conversations

    const query = searchQuery.toLowerCase()
    return conversations.filter(
      (conversation) =>
        conversation.customerName?.toLowerCase().includes(query) ||
        conversation.customerPhone.includes(query) ||
        conversation.lastMessageContent?.toLowerCase().includes(query) ||
        conversation.businessPhone.includes(query) ||
        conversation.storeName?.toLowerCase().includes(query)
    )
  }, [conversations, searchQuery])

  // Get unread notification count for each conversation
  const getUnreadNotificationCount = useCallback(
    (conversationId: string): number => {
      return unreadNotifications.filter(
        (n) => n.conversationId === conversationId
      ).length
    },
    [unreadNotifications]
  )

  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true)
    try {
      await refetch()
    } finally {
      setIsRefreshing(false)
    }
  }, [refetch])

  const handleConversationSelect = useCallback(
    (conversationId: string) => {
      // Clear unread count for this conversation
      clearUnreadCount(conversationId)
      onConversationSelect(conversationId)
    },
    [onConversationSelect, clearUnreadCount]
  )

  useEffect(() => {
    filteredConversations &&
      onConversationSelect(filteredConversations?.[0]?.id)
  }, [filteredConversations, onConversationSelect])

  // Loading state
  if (loading && conversations.length === 0) {
    return (
      <div className={cn("h-full flex flex-col bg-[#111b21]", className)}>
        <div className="flex-1 flex items-center justify-center">
          <div className="flex flex-col items-center space-y-3">
            <Loader2 className="h-8 w-8 animate-spin text-[#00a884]" />
            <p className="text-[#8696a0] text-sm">Loading conversations...</p>
          </div>
        </div>
      </div>
    )
  }

  // Error state
  if (error && conversations.length === 0) {
    return (
      <div className={cn("h-full flex flex-col bg-[#111b21]", className)}>
        <div className="flex-1 flex items-center justify-center p-4">
          <div className="text-center space-y-4 max-w-sm">
            <div className="w-16 h-16 mx-auto bg-[#2a3942] rounded-full flex items-center justify-center">
              <AlertCircle className="h-8 w-8 text-red-400" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-[#e9edef] mb-2">
                Unable to Load Conversations
              </h3>
              <p className="text-[#8696a0] text-sm mb-4">
                {error ||
                  "Something went wrong while loading your conversations."}
              </p>
              <Button
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="bg-[#00a884] hover:bg-[#008069] text-white"
              >
                {isRefreshing ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Retrying...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Try Again
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Empty state
  if (!loading && filteredConversations.length === 0) {
    return (
      <div className={cn("h-full flex flex-col bg-[#111b21]", className)}>
        <div className="flex-1 flex items-center justify-center p-4">
          <div className="text-center space-y-4 max-w-sm">
            <div className="w-16 h-16 mx-auto bg-[#2a3942] rounded-full flex items-center justify-center">
              <svg
                className="h-8 w-8 text-[#8696a0]"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1.5}
                  d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-chat-gray-900 mb-2">
                {searchQuery
                  ? "No conversations found"
                  : "No conversations yet"}
              </h3>
              <p className="text-chat-gray-600 text-sm">
                {searchQuery
                  ? "Try adjusting your search or filters"
                  : "When customers message you, their conversations will appear here"}
              </p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  console.log({ filteredConversations })
  return (
    <div
      className={cn("h-full flex flex-col", className)}
      style={{
        background: "linear-gradient(to bottom, #111b21 0%, #0b141a 100%)",
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
      }}
    >
      {/* Conversation List */}
      <div className="flex-1 overflow-hidden">
        <div className="h-full overflow-y-auto scrollbar-thin scrollbar-thumb-[#2a3942] scrollbar-track-transparent">
          <div className="space-y-0">
            {filteredConversations.map((conversation) => (
              <ConversationItem
                key={conversation.id}
                conversation={conversation}
                isSelected={selectedConversationId === conversation.id}
                onClick={() => handleConversationSelect(conversation.id)}
                unreadNotificationCount={getUnreadNotificationCount(
                  conversation.conversationId
                )}
                className={cn(
                  "border-b border-[#2a3942]/30 last:border-b-0",
                  "hover:bg-[#2a3942]/50 transition-all duration-200",
                  selectedConversationId === conversation.id &&
                    "bg-[#2a3942]/70 hover:bg-[#2a3942]/70"
                )}
              />
            ))}
          </div>
        </div>
      </div>

      {/* Pull to Refresh Indicator */}
      {/* {isRefreshing && (
        <div className="absolute top-0 left-0 right-0 bg-chat-primary/10 backdrop-blur-sm p-2">
          <div className="flex items-center justify-center space-x-2">
            <Loader2 className="h-4 w-4 animate-spin text-chat-primary" />
            <span className="text-sm text-chat-primary font-medium">Refreshing...</span>
          </div>
        </div>
      )} */}
    </div>
  )
}

export default ConversationList
