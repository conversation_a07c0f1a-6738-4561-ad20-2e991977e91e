import React, { useMemo } from "react";
import { formatDistanceToNow } from "date-fns";
import { Badge } from "../ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Store, CheckCheck, Check } from "lucide-react";
import { cn } from "../../lib/utils";
import type { ConversationItemProps, ConversationStatus } from "../../types/chat";

const ConversationItem: React.FC<ConversationItemProps> = ({
  conversation,
  isSelected = false,
  onClick,
  // Fallback external count if needed
  unreadNotificationCount = 0,
  className,
}) => {
  // ------- Helpers -------
  const safeLastMessageAt = conversation.lastMessageAt ?? conversation.updatedAt ?? null;

  const formatTimestamp = (timestamp: string | null) => {
    if (!timestamp) return "";
    try {
      const date = new Date(timestamp);
      const now = new Date();
      const diffHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

      if (diffHours < 1) {
        return formatDistanceToNow(date, { addSuffix: false })
          .replace(" minutes", " mins")
          .replace(" minute", " min");
      } else if (diffHours < 24) {
        return date.toLocaleTimeString("en-US", {
          hour: "numeric",
          minute: "2-digit",
          hour12: true,
        });
      } else if (diffHours < 168) {
        return date.toLocaleDateString("en-US", { weekday: "short" });
      } else {
        return date.toLocaleDateString("en-US", {
          month: "short",
          day: "numeric",
        });
      }
    } catch {
      return "";
    }
  };

  const getStatusConfig = (status: ConversationStatus) => {
    switch (status) {
      case "OPEN":
        return { color: "bg-emerald-50 text-emerald-700 border-emerald-200", icon: "🟢", label: "Open" };
      case "ASSIGNED":
        return { color: "bg-blue-50 text-blue-700 border-blue-200", icon: "👤", label: "Assigned" };
      case "RESOLVED":
        return { color: "bg-gray-50 text-gray-700 border-gray-200", icon: "✅", label: "Resolved" };
      case "CLOSED":
        return { color: "bg-red-50 text-red-700 border-red-200", icon: "🔒", label: "Closed" };
      case "ARCHIVED":
        return { color: "bg-orange-50 text-orange-700 border-orange-200", icon: "📦", label: "Archived" };
      default:
        return { color: "bg-gray-50 text-gray-700 border-gray-200", icon: "❓", label: status };
    }
  };

  // WhatsApp-like last message preview (supports JSON payloads & media hints)
  const formatMessagePreview = (raw: string | null | undefined) => {
    if (!raw || raw.trim() === "") {
      return conversation.messageCount > 0 ? "Tap to view conversation" : "No messages yet";
    }

    // Try JSON first
    try {
      const parsed = JSON.parse(raw);
      if (parsed && typeof parsed === "object") {
        const type = String(parsed.type || "").toLowerCase();
        const text = parsed.text as string | undefined;

        if (type === "text") {
          return truncate(text || "Text message");
        }
        if (type === "image") return "📷 Photo";
        if (type === "video") return "🎥 Video";
        if (type === "audio") return "🎵 Voice message";
        if (type === "document") return "📎 Document";
        if (type === "location") return "📍 Location";
        if (type === "contacts") return "👤 Contact";
        if (type === "sticker") return "😄 Sticker";
        if (text) return truncate(text);
        return "New message";
      }
    } catch {
      // not JSON, fall through
    }

    const content = raw.toLowerCase();
    if (content.includes("location") || raw.includes("📍")) return "📍 Location";
    if (content.includes("document") || raw.includes("📎")) return "📎 Document";
    if (content.includes("image") || raw.includes("🖼️") || raw.includes("📷")) return "📷 Photo";
    if (content.includes("video") || raw.includes("🎥")) return "🎥 Video";
    if (content.includes("audio") || raw.includes("🎵")) return "🎵 Voice message";

    return truncate(raw);
  };

  const truncate = (s: string, n = 45) => (s.length > n ? s.slice(0, n) + "..." : s);

  // Determine direction/meta for ticks like WhatsApp
  // Expect optional fields:
  // - lastMessageDirection: "out" | "in"
  // - lastMessageRead: boolean (if your data provides read receipts)
  const lastDir = (conversation as any).lastMessageDirection as "out" | "in" | undefined;
  const lastRead = Boolean((conversation as any).lastMessageRead);

  const showOutboundStatusTick = useMemo(() => lastDir === "out", [lastDir]);

  const unreadCount =
    (typeof conversation.unreadCount === "number" ? conversation.unreadCount : undefined) ??
    (typeof unreadNotificationCount === "number" ? unreadNotificationCount : 0);

  const isUnread = unreadCount > 0;

  const statusConfig = getStatusConfig(conversation.status);

  return (
    <div
      className={cn(
        "flex items-center gap-3 px-4 py-3 cursor-pointer transition-all duration-200",
        // WhatsApp list bg/hover
        "hover:bg-[#2a3942]",
        "border-b border-[#2a3942]",
        isSelected && "bg-[#2a3942]",
        className
      )}
      onClick={onClick}
      role="button"
      aria-label={`Open conversation with ${conversation.customerName || conversation.customerPhone || "Unknown"}`}
    >
      {/* Avatar */}
      <div className="flex-shrink-0 relative">
        <Avatar className="w-12 h-12">
          <AvatarImage
            src={conversation.customerProfilePicture || undefined}
            alt={conversation.customerName || "Customer"}
          />
          <AvatarFallback className="bg-[#6b7c85] text-white font-medium">
            {(conversation.customerName || conversation.customerPhone || "U").charAt(0).toUpperCase()}
          </AvatarFallback>
        </Avatar>

        {/* Unread bubble (top-right) */}
        {isUnread && (
          <div className="absolute -top-1 -right-1 w-5 h-5 bg-[#25D366] rounded-full flex items-center justify-center">
            <span className="text-xs text-white font-semibold">
              {unreadCount > 99 ? "99+" : unreadCount}
            </span>
          </div>
        )}
      </div>

      {/* Main */}
      <div className="flex-1 min-w-0">
        {/* Header */}
        <div className="flex items-center justify-between mb-1">
          <h3
            className={cn(
              "truncate",
              "text-[#e9edef]",
              isUnread ? "font-semibold" : "font-medium"
            )}
            title={conversation.customerName || conversation.customerPhone || "Unknown Contact"}
          >
            {conversation.customerName || conversation.customerPhone || "Unknown Contact"}
          </h3>

          {/* Timestamp */}
          <span
            className={cn(
              "text-xs flex-shrink-0",
              isUnread ? "text-[#25D366] font-semibold" : "text-[#8696a0]"
            )}
          >
            {formatTimestamp(safeLastMessageAt)}
          </span>
        </div>

        {/* Preview + right-side pills */}
        <div className="flex items-center justify-between gap-2">
          <div className="flex min-w-0 items-center gap-2 flex-1">
            {/* Ticks only for outbound last message (WhatsApp behavior) */}
            {showOutboundStatusTick && (
              lastRead ? (
                <CheckCheck className="w-4 h-4 text-[#53bdeb] flex-shrink-0" aria-label="Read" />
              ) : (
                <Check className="w-4 h-4 text-[#8696a0] flex-shrink-0" aria-label="Sent" />
              )
            )}

            {/* "You:" hint for outbound text */}
            {lastDir === "out" && (
              <span className={cn("text-sm flex-shrink-0", isUnread ? "text-[#e9edef]" : "text-[#8696a0]")}>
                You:
              </span>
            )}

            <p
              className={cn(
                "text-sm truncate",
                isUnread ? "font-medium text-[#e9edef]" : "text-[#8696a0]"
              )}
              title={conversation.lastMessageContent || undefined}
            >
              {formatMessagePreview(conversation.lastMessageContent ?? null)}
            </p>
          </div>

          <div className="flex items-center gap-2 flex-shrink-0">
            {/* Green unread count pill (WhatsApp) on the far right */}
            {isUnread && (
              <div className="bg-[#25D366] text-white text-xs font-semibold px-2 py-0.5 rounded-full min-w-[20px] h-5 flex items-center justify-center">
                {unreadCount > 99 ? "99+" : unreadCount}
              </div>
            )}

            {/* Optional store badge (kept subtle to match WA) */}
            {conversation.storeName && (
              <Badge
                variant="outline"
                className="text-xs bg-[#2a3942] text-[#8696a0] border-[#2a3942]"
                title={conversation.storeName}
              >
                <Store className="w-2.5 h-2.5 mr-1" />
                {conversation.storeName}
              </Badge>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConversationItem;