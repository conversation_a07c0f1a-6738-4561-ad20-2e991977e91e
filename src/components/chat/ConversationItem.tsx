import React from "react"
import { formatDistanceToNow } from "date-fns"
import { Badge } from "../ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar"
import { Store, CheckCheck } from "lucide-react"
import { cn } from "../../lib/utils"
import type {
  ConversationItemProps,
  ConversationStatus
} from "../../types/chat"

const ConversationItem: React.FC<ConversationItemProps> = ({
  conversation,
  isSelected = false,
  onClick,
  unreadNotificationCount = 0,
  className
}) => {
  // Format timestamp for display
  const formatTimestamp = (timestamp: string) => {
    try {
      const date = new Date(timestamp)
      const now = new Date()
      const diffHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

      if (diffHours < 1) {
        return formatDistanceToNow(date, { addSuffix: false }).replace(
          "minute",
          "min"
        )
      } else if (diffHours < 24) {
        return date.toLocaleTimeString("en-US", {
          hour: "numeric",
          minute: "2-digit",
          hour12: true
        })
      } else if (diffHours < 168) {
        // Less than a week
        return date.toLocaleDateString("en-US", { weekday: "short" })
      } else {
        return date.toLocaleDateString("en-US", {
          month: "short",
          day: "numeric"
        })
      }
    } catch {
      return "Unknown time"
    }
  }

  // Enhanced status display with better colors
  const getStatusConfig = (status: ConversationStatus) => {
    switch (status) {
      case "OPEN":
        return {
          color: "bg-emerald-50 text-emerald-700 border-emerald-200",
          icon: "🟢",
          label: "Open"
        }
      case "ASSIGNED":
        return {
          color: "bg-blue-50 text-blue-700 border-blue-200",
          icon: "👤",
          label: "Assigned"
        }
      case "RESOLVED":
        return {
          color: "bg-gray-50 text-gray-700 border-gray-200",
          icon: "✅",
          label: "Resolved"
        }
      case "CLOSED":
        return {
          color: "bg-red-50 text-red-700 border-red-200",
          icon: "🔒",
          label: "Closed"
        }
      case "ARCHIVED":
        return {
          color: "bg-orange-50 text-orange-700 border-orange-200",
          icon: "📦",
          label: "Archived"
        }
      default:
        return {
          color: "bg-gray-50 text-gray-700 border-gray-200",
          icon: "❓",
          label: status
        }
    }
  }

  // Extract and format phone numbers for better display
  const formatPhoneForDisplay = (phone: string) => {
    if (!phone) return ""

    // Format phone number with proper spacing for readability
    // Example: +971581454113 -> +971 58 145 4113
    const cleaned = phone.replace(/\D/g, "")
    if (cleaned.length >= 10) {
      // International format
      if (cleaned.startsWith("971")) {
        return `+971 ${cleaned.slice(3, 5)} ${cleaned.slice(
          5,
          8
        )} ${cleaned.slice(8)}`
      } else if (cleaned.startsWith("1")) {
        return `+1 ${cleaned.slice(1, 4)} ${cleaned.slice(
          4,
          7
        )} ${cleaned.slice(7)}`
      }
    }
    return phone
  }

  // Determine online status based on last activity
  const getOnlineStatus = () => {
    if (!conversation.lastMessageAt) return "offline"

    const lastActivity = new Date(conversation.lastMessageAt)
    const now = new Date()
    const diffMinutes = (now.getTime() - lastActivity.getTime()) / 60000

    if (diffMinutes < 5) return "online"
    if (diffMinutes < 30) return "away"
    return "offline"
  }

  // Enhanced message preview with better formatting and type detection
  const formatMessagePreview = (content: string | null) => {
    if (!content) {
      return conversation.messageCount > 0
        ? "Tap to view conversation"
        : "No messages yet"
    }

    // Try to parse JSON content first (from organization-wide notifications)
    try {
      const parsed = JSON.parse(content)
      if (parsed && typeof parsed === "object") {
        // Handle different message types based on parsed content
        switch (parsed.type?.toLowerCase()) {
          case "text":
            return parsed.text
              ? parsed.text.length > 45
                ? parsed.text.substring(0, 45) + "..."
                : parsed.text
              : "Text message"
          case "image":
            return "📷 Image"
          case "video":
            return "🎥 Video"
          case "audio":
            return "🎵 Voice message"
          case "document":
            return "📎 Document"
          case "location":
            return "📍 Location"
          case "contacts":
            return "👤 Contact"
          case "sticker":
            return "😄 Sticker"
          default:
            // If it has a text field, use that
            if (parsed.text) {
              return parsed.text.length > 45
                ? parsed.text.substring(0, 45) + "..."
                : parsed.text
            }
            return "New message"
        }
      }
    } catch (e) {
      // Not JSON, continue with string processing
    }

    // Handle plain string content (legacy format)
    // Detect message types and format accordingly
    if (content.includes("📍") || content.toLowerCase().includes("location")) {
      return "📍 Location shared"
    }
    if (content.includes("📎") || content.toLowerCase().includes("document")) {
      return "📎 Document shared"
    }
    if (content.includes("🖼️") || content.toLowerCase().includes("image")) {
      return "🖼️ Photo shared"
    }
    if (content.includes("🎥") || content.toLowerCase().includes("video")) {
      return "🎥 Video shared"
    }
    if (content.includes("🎵") || content.toLowerCase().includes("audio")) {
      return "🎵 Voice message"
    }

    // Format text content
    const truncated =
      content.length > 45 ? content.substring(0, 45) + "..." : content
    return truncated
  }

  // Get message status indicator
  const getMessageStatusIcon = () => {
    if (conversation.unreadCount > 0) {
      return null // No status icon for unread messages
    }

    // For read messages, show read indicators
    return <CheckCheck className="w-3 h-3 text-blue-500" />
  }

  const statusConfig = getStatusConfig(conversation.status)
  const isUnread = conversation.unreadCount > 0
  console.log({ conversation })
  return (
    <div
      className={cn(
        "flex items-center gap-3 px-4 py-3 cursor-pointer transition-all duration-200",
        "hover:bg-[#2a3942]",
        "border-b border-[#2a3942]",
        // Selected conversation (currently viewing) - WhatsApp style
        isSelected && "bg-[#2a3942]",
        className
      )}
      onClick={onClick}
    >
      {/* Avatar - WhatsApp Style */}
      <div className="flex-shrink-0 relative">
        <Avatar className="w-12 h-12">
          <AvatarImage
            src={conversation.customerProfilePicture}
            alt={conversation.customerName || "Customer"}
          />
          <AvatarFallback className="bg-[#6b7c85] text-white font-medium">
            {(conversation.customerName || conversation.customerPhone || "U")
              .charAt(0)
              .toUpperCase()}
          </AvatarFallback>
        </Avatar>

        {/* Unread indicator */}
        {isUnread && (
          <div className="absolute -top-1 -right-1 w-5 h-5 bg-chat-unread dark:bg-chat-unread-dark rounded-full flex items-center justify-center">
            <span className="text-xs text-white font-medium">
              {conversation.unreadCount > 99 ? "99+" : conversation.unreadCount}
            </span>
          </div>
        )}
      </div>

      {/* Main Content - WhatsApp Style */}
      <div className="flex-1 min-w-0">
        {/* Header Row */}
        <div className="flex items-center justify-between mb-1">
          <h3
            className={cn(
              "font-medium text-[#e9edef] truncate",
              isUnread && "font-semibold"
            )}
          >
            {conversation.customerName ||
              conversation.customerPhone ||
              "Unknown Contact"}
          </h3>

          {/* Timestamp - WhatsApp Style */}
          <div className="flex items-center gap-1 flex-shrink-0">
            <span
              className={cn(
                "text-xs text-[#8696a0]",
                isUnread && "text-[#00a884] font-medium"
              )}
            >
              {formatTimestamp(conversation.lastMessageAt)}
            </span>
          </div>
        </div>

        {/* Message Preview Row - WhatsApp Style */}
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0 flex items-center gap-2">
            {/* Message status icon for outbound messages */}
            {getMessageStatusIcon()}

            <p
              className={cn(
                "text-sm truncate flex-1",
                isUnread ? "font-medium text-[#e9edef]" : "text-[#8696a0]"
              )}
            >
              {formatMessagePreview(conversation.lastMessageContent ?? null)}
            </p>
          </div>

          <div className="flex items-center gap-2 flex-shrink-0">
            {/* Unread count badge - WhatsApp Style */}
            {isUnread && conversation.unreadCount > 0 && (
              <div className="bg-[#00a884] text-white text-xs font-medium px-2 py-1 rounded-full min-w-[20px] h-5 flex items-center justify-center">
                {conversation.unreadCount > 99
                  ? "99+"
                  : conversation.unreadCount}
              </div>
            )}

            {/* Store badge if available */}
            {conversation.storeName && (
              <Badge
                variant="outline"
                className="text-xs bg-[#2a3942] text-[#8696a0] border-[#2a3942]"
              >
                <Store className="w-2.5 h-2.5 mr-1" />
                {conversation.storeName}
              </Badge>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default ConversationItem
