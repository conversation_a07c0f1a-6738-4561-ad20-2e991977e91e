import React, { useRef } from 'react';
import { Button } from '@/components/ui/button';
import { 
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Paperclip,
  ImageIcon,
  Mic,
  Camera,
  MapPin,
  User,
  Smile,
  FolderOpen,
  BarChart3,
  Calendar,
  Scissors
} from 'lucide-react';

export interface MediaType {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  accept: string;
  maxSize: number; // in bytes
  description: string;
  color: string;
}



export const MEDIA_TYPES: MediaType[] = [
  {
    id: 'image',
    label: 'Photos & videos',
    icon: ImageIcon,
    accept: 'image/*,video/*',
    maxSize: 16 * 1024 * 1024, // 16MB for images, WhatsApp supports up to 5MB but we can optimize
    description: 'Share images and videos',
    color: 'bg-blue-500'
  },
  {
    id: 'document',
    label: 'File',
    icon: FolderOpen,
    accept: '.pdf,.doc,.docx,.txt,.xls,.xlsx,.ppt,.pptx',
    maxSize: 100 * 1024 * 1024, // 100MB for documents (WhatsApp limit)
    description: 'Share files and documents',
    color: 'bg-indigo-500'
  },
  {
    id: 'camera',
    label: 'Camera',
    icon: Camera,
    accept: 'image/*',
    maxSize: 16 * 1024 * 1024,
    description: 'Take a photo',
    color: 'bg-pink-500'
  },
  {
    id: 'location',
    label: 'Location',
    icon: MapPin,
    accept: '',
    maxSize: 0,
    description: 'Share your location',
    color: 'bg-green-500'
  },
  {
    id: 'contact',
    label: 'Contact',
    icon: User,
    accept: '',
    maxSize: 0,
    description: 'Share contact information',
    color: 'bg-blue-600'
  },
  {
    id: 'poll',
    label: 'Poll',
    icon: BarChart3,
    accept: '',
    maxSize: 0,
    description: 'Create a poll',
    color: 'bg-orange-500'
  },
  {
    id: 'event',
    label: 'Event',
    icon: Calendar,
    accept: '',
    maxSize: 0,
    description: 'Create an event',
    color: 'bg-purple-500'
  }
];

interface MediaAttachmentMenuProps {
  onMediaTypeSelect: (mediaType: MediaType, file?: File) => void;
  disabled?: boolean;
  className?: string;
}

export const MediaAttachmentMenu: React.FC<MediaAttachmentMenuProps> = ({
  onMediaTypeSelect,
  disabled = false,
  className = ""
}) => {
  const fileInputRefs = useRef<{ [key: string]: HTMLInputElement | null }>({});

  const handleFileInputChange = (mediaType: MediaType, event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      onMediaTypeSelect(mediaType, file);
    }
    // Reset input value to allow selecting the same file again
    event.target.value = '';
  };

  const handleMediaTypeClick = (mediaType: MediaType) => {
    if (mediaType.accept) {
      // For file uploads, trigger the file input
      fileInputRefs.current[mediaType.id]?.click();
    } else {
      // For special types like location and contact, call directly
      onMediaTypeSelect(mediaType);
    }
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className={`h-10 w-10 p-0 hover:bg-[#3b4a54] rounded-full transition-all duration-200 text-[#8696a0] hover:text-[#e9edef] hover:scale-110 ${className}`}
          disabled={disabled}
          title="Attach media"
        >
          <Paperclip className="h-5 w-5" />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="w-72 p-0 bg-[#2a3942] border-[#3b4a54] shadow-2xl"
        side="top"
        align="start"
        sideOffset={8}
      >
          {/* Hidden file inputs */}
          {MEDIA_TYPES.map((mediaType) => (
            mediaType.accept && (
              <input
                key={`input-${mediaType.id}`}
                type="file"
                ref={(el) => {
                  fileInputRefs.current[mediaType.id] = el;
                }}
                accept={mediaType.accept}
                onChange={(e) => handleFileInputChange(mediaType, e)}
                className="hidden"
                capture={mediaType.id === 'camera' ? 'environment' : undefined}
              />
            )
          ))}

          {/* WhatsApp-style menu */}
          <div className="p-3">
            <div className="grid grid-cols-3 gap-4">
              {MEDIA_TYPES.map((mediaType) => {
                const IconComponent = mediaType.icon;
                return (
                  <button
                    key={mediaType.id}
                    className="flex flex-col items-center gap-2 p-3 rounded-xl hover:bg-[#3b4a54] transition-all duration-200 group"
                    onClick={() => handleMediaTypeClick(mediaType)}
                    disabled={disabled}
                  >
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0 ${mediaType.color} group-hover:scale-110 transition-transform duration-200`}>
                      <IconComponent className="h-6 w-6 text-white" />
                    </div>
                    <span className="text-xs font-medium text-[#e9edef] text-center leading-tight">{mediaType.label}</span>
                  </button>
                );
              })}
            </div>
          </div>
      </PopoverContent>
    </Popover>
  );
}; 