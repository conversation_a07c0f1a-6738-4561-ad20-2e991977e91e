import { useState, useCallback, useRef, useEffect } from 'react';
import { generateClient } from 'aws-amplify/api';
import { executeSubscription } from '@/lib/graphql-client';
import { ON_MEDIA_UPLOAD_STATUS_CHANGED } from '@/lib/graphql-operations';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { uploadFileToS3 } from '@/lib/s3/upload';
import { 
  SEND_MESSAGE, 
  SendMessageInput, 
  SendMessageResponse 
} from '@/lib/graphql/mutations/sendMessage';
import { 
  Send, 
  Smile,
  Mic
} from 'lucide-react';
import { MediaAttachmentMenu, MediaType } from './MediaAttachmentMenu';
import { MediaPreview, MediaUpload as MediaUploadType } from './MediaPreview';
import { LocationDialog, LocationData } from './LocationDialog';
import { ContactDialog, ContactData } from './ContactDialog';

interface MessageComposerProps {
  conversationId: string;
  onMessageSent?: (message: any) => void;
  disabled?: boolean;
  className?: string;
}

export const MessageComposer: React.FC<MessageComposerProps> = ({
  conversationId,
  onMessageSent,
  disabled = false,
  className = ""
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  
  // State
  const [textContent, setTextContent] = useState('');
  const [sending, setSending] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [mediaUpload, setMediaUpload] = useState<MediaUploadType | null>(null);
  const [isLocationDialogOpen, setIsLocationDialogOpen] = useState(false);
  const [isContactDialogOpen, setIsContactDialogOpen] = useState(false);
  
  // Initialize Amplify GraphQL client
  const client = generateClient();

  // Subscribe to media upload status updates
  useEffect(() => {
    if (!mediaUpload || mediaUpload.status === 'UPLOADING') return;
    
    // Only subscribe for file-based uploads that have s3Key
    if (mediaUpload.messageType === 'LOCATION' || mediaUpload.messageType === 'CONTACT') return;

    console.log('🎬 Setting up media upload subscription for conversation:', conversationId);
    
    const subscription = executeSubscription(
      {
        query: ON_MEDIA_UPLOAD_STATUS_CHANGED,
        variables: { conversationId }
      },
      {
        next: (data: any) => {
          console.log('🎬 Media upload status update:', data);
          const update = data.onMediaUploadStatusChanged;
          console.log(`🔍 Comparing s3Keys - Frontend: "${mediaUpload.s3Key}" vs Backend: "${update?.s3Key}"`);
          
          // Match by filename since s3Key formats may differ (uploads/ vs public/uploads/)
          const frontendFilename = 's3Key' in mediaUpload ? mediaUpload.s3Key.split('/').pop() : '';
          const backendFilename = update?.s3Key.split('/').pop();
          
          if (update && frontendFilename && backendFilename && frontendFilename === backendFilename) {
            setMediaUpload(prev => prev ? {
              ...prev,
              status: update.status, // Keep backend status format (uppercase)
              mediaId: update.mediaId || ('mediaId' in prev ? prev.mediaId : undefined),
              fileName: update.fileName || ('fileName' in prev ? prev.fileName : ''),
              fileSize: update.fileSize !== undefined ? update.fileSize : ('fileSize' in prev ? prev.fileSize : 0)
            } : null);
            
            console.log(`🎬 Updated media upload status to: ${update.status}`, update);
            
            // Show success toast when ready
            if (update.status === 'READY') {
              console.log('🎉 Media is READY! Showing toast...');
              toast({
                title: "✅ Media Ready",
                description: "Your media is ready to send!",
              });
            }
          }
        },
        error: (error: any) => {
          console.error('Media upload subscription error:', error);
          toast({
            title: "Connection Error",
            description: "Lost connection to upload updates",
            variant: "destructive",
          });
        }
      }
    );

    return () => {
      subscription?.unsubscribe();
    };
  }, [mediaUpload && 's3Key' in mediaUpload ? mediaUpload.s3Key : null, conversationId, toast]);

  const adjustTextareaHeight = useCallback(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      const newHeight = Math.min(Math.max(textarea.scrollHeight, 40), 120);
      textarea.style.height = newHeight + 'px';
    }
  }, []);

  const handleMediaTypeSelect = async (mediaType: MediaType, file?: File) => {
    if (!user?.organizationId) return;

    // Handle location and contact types
    if (mediaType.id === 'location') {
      setIsLocationDialogOpen(true);
      return;
    }

    if (mediaType.id === 'contact') {
      setIsContactDialogOpen(true);
      return;
    }

    // Handle file uploads
    if (!file) return;

    // Validate file type and size
    if (file.size > mediaType.maxSize) {
      const sizeInMB = Math.round(mediaType.maxSize / (1024 * 1024));
      toast({
        title: "File Too Large",
        description: `Please select a file smaller than ${sizeInMB}MB`,
        variant: "destructive",
      });
      return;
    }

    // Extract phoneNumberId from conversationId
    const conversationParts = conversationId.split('#');
    const phoneNumberId = conversationParts.length >= 2 ? conversationParts[1] : '';
    
    if (!phoneNumberId) {
      toast({
        title: "Upload Failed",
        description: "Could not determine phone number ID from conversation.",
        variant: "destructive",
      });
      return;
    }

    // Create preview URL for supported types
    const previewUrl = file.type.startsWith('image/') || file.type.startsWith('video/') || file.type.startsWith('application/') 
      ? URL.createObjectURL(file) : undefined;

    // Determine message type based on media type
    let messageType: MediaUploadType['messageType'];
    switch (mediaType.id) {
      case 'image':
      case 'camera':
        messageType = file.type.startsWith('video/') ? 'VIDEO' : 'IMAGE';
        break;
      case 'document':
        messageType = 'DOCUMENT';
        break;
      case 'audio':
        messageType = 'AUDIO';
        break;
      case 'sticker':
        messageType = 'STICKER';
        break;
      default:
        messageType = 'IMAGE';
    }

    // Set initial upload state
    setMediaUpload({
      s3Key: '',
      fileName: file.name,
      mediaType: file.type,
      status: 'UPLOADING',
      fileSize: file.size,
      progress: 0,
      previewUrl,
      messageType
    });

    toast({
      title: "📤 Uploading Media",
      description: `Preparing your ${mediaType.label.toLowerCase()} for upload...`,
    });
    
    const uploadResult = await uploadFileToS3(file, user.organizationId, conversationId, phoneNumberId, (progress) => {
      console.log(`Upload progress:`, progress);
      if (progress && progress.loaded !== undefined && progress.total !== undefined) {
        const percentage = Math.round((progress.loaded / progress.total) * 100);
        setMediaUpload(prev => prev ? {
          ...prev,
          progress: percentage
        } : null);
      }
    });

    if (uploadResult.success && uploadResult.key) {
      // Update to processing state
      setMediaUpload(prev => prev ? {
        ...prev,
        s3Key: uploadResult.key!,
        status: 'PROCESSING',
        progress: 100
      } : null);

      toast({
        title: "🔄 Processing Media",
        description: "Optimizing your media for WhatsApp...",
      });
    } else {
      // Clean up preview URL
      if (previewUrl) URL.revokeObjectURL(previewUrl);
      setMediaUpload(null);
      
      toast({
        title: "Upload Failed",
        description: "Could not upload the media. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleLocationSelect = (location: LocationData) => {
    setMediaUpload({
      messageType: 'LOCATION',
      latitude: location.latitude,
      longitude: location.longitude,
      name: location.name,
      address: location.address,
      status: 'READY'
    });
    
    toast({
      title: "📍 Location Added",
      description: "Location is ready to send!",
    });
  };

  const handleContactSelect = (contact: ContactData) => {
    setMediaUpload({
      messageType: 'CONTACT',
      name: contact.name,
      phoneNumber: contact.phoneNumber,
      email: contact.email,
      organization: contact.organization,
      address: contact.address,
      birthday: contact.birthday,
      url: contact.url,
      status: 'READY'
    });
    
    toast({
      title: "👤 Contact Added",
      description: "Contact is ready to send!",
    });
  };

  const handleRemoveMedia = useCallback(() => {
    if (mediaUpload && 'previewUrl' in mediaUpload && mediaUpload.previewUrl) {
      URL.revokeObjectURL(mediaUpload.previewUrl);
    }
    setMediaUpload(null);
    toast({
      title: "Media Removed",
      description: "Media removed from message",
    });
  }, [mediaUpload, toast]);

  const handleTextChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setTextContent(e.target.value);
    adjustTextareaHeight();
  }, [adjustTextareaHeight]);

  const handleSend = useCallback(async () => {
    const hasText = textContent.trim().length > 0;
    const hasMedia = mediaUpload && mediaUpload.status === 'READY';

    if ((!hasText && !hasMedia) || !user?.organizationId || sending) return;
    
    setSending(true);
    
    try {
      let input: SendMessageInput = {
        conversationId,
        organizationId: user.organizationId,
        messageType: hasMedia ? mediaUpload.messageType : 'TEXT',
      };

      // Handle different message types
      if (hasMedia) {
        switch (mediaUpload.messageType) {
          case 'LOCATION':
            const locationUpload = mediaUpload as Extract<MediaUploadType, { messageType: 'LOCATION' }>;
            input = {
              ...input,
              messageType: 'LOCATION',
              latitude: locationUpload.latitude,
              longitude: locationUpload.longitude,
              locationName: locationUpload.name,
              locationAddress: locationUpload.address,
            };
            break;
            
          case 'CONTACT':
            const contactUpload = mediaUpload as Extract<MediaUploadType, { messageType: 'CONTACT' }>;
            input = {
              ...input,
              messageType: 'CONTACT',
              contacts: [{
                name: contactUpload.name,
                phoneNumber: contactUpload.phoneNumber,
                email: contactUpload.email,
                organization: contactUpload.organization,
                address: contactUpload.address,
                birthday: contactUpload.birthday,
                url: contactUpload.url,
              }],
            };
            break;
            
          default:
            // File-based media types
            if ('s3Key' in mediaUpload && 'fileName' in mediaUpload) {
              input = {
                ...input,
                mediaUrl: mediaUpload.s3Key,
                mediaType: mediaUpload.mediaType,
                fileName: mediaUpload.fileName,
                fileSize: mediaUpload.fileSize,
                caption: hasText ? textContent.trim() : undefined,
              };
            }
        }
        
        // For media messages with text, use caption instead of text
        if (hasText && mediaUpload.messageType !== 'LOCATION' && mediaUpload.messageType !== 'CONTACT') {
          input.text = undefined;
        }
      } else {
        // Text-only message
        input.text = textContent.trim();
      }
      
      const result = await client.graphql({
        query: SEND_MESSAGE,
        variables: { input }
      });
      
      const response = (result as any).data?.sendMessage as SendMessageResponse;
      
      if (response?.success) {
        setTextContent('');
        
        // Clean up media upload
        if (mediaUpload && 'previewUrl' in mediaUpload && mediaUpload.previewUrl) {
          URL.revokeObjectURL(mediaUpload.previewUrl);
        }
        setMediaUpload(null);
        
        if (textareaRef.current) {
          textareaRef.current.style.height = '40px';
        }
        onMessageSent?.(response.message);
        
        toast({
          title: "✅ Message Sent",
          description: "Your message has been delivered",
        });
      } else {
        toast({
          title: "Failed to send message",
          description: response?.error?.message || 'Failed to send message',
          variant: "destructive",
        });
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error?.message || 'An unexpected error occurred',
        variant: "destructive",
      });
    } finally {
      setSending(false);
    }
  }, [textContent, mediaUpload, user?.organizationId, sending, conversationId, client, onMessageSent, toast]);

  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  }, [handleSend]);

  const handleImageProcess = useCallback(async (processedFile: File) => {
    if (!mediaUpload || mediaUpload.messageType !== 'IMAGE') return;

    try {
      // Create new preview URL for the processed file
      const newPreviewUrl = URL.createObjectURL(processedFile);

      // Update the media upload with the processed file
      setMediaUpload({
        ...mediaUpload,
        file: processedFile,
        previewUrl: newPreviewUrl,
        fileName: processedFile.name,
        fileSize: processedFile.size,
        status: 'READY'
      });

      toast({
        title: "Image processed",
        description: "Your image has been processed successfully",
      });
    } catch (error) {
      console.error('Failed to process image:', error);
      toast({
        title: "Processing failed",
        description: "Failed to process the image. Please try again.",
        variant: "destructive"
      });
    }
  }, [mediaUpload, toast]);

  const handleVoiceRecord = useCallback(() => {
    if (isRecording) {
      // Stop recording
      setIsRecording(false);
      toast({
        title: "Voice recording stopped",
        description: "Voice message functionality coming soon",
      });
    } else {
      // Start recording
      setIsRecording(true);
      toast({
        title: "Voice recording started",
        description: "Voice message functionality coming soon",
      });
    }
  }, [isRecording, toast]);

  const canSend = !sending && !disabled && user?.organizationId && 
                  (textContent.trim().length > 0 || (mediaUpload?.status === 'READY'));
  const showMicButton = !textContent.trim() && !sending && !mediaUpload;

  const getPlaceholderText = () => {
    if (!mediaUpload) return "Type a message";
    
    switch (mediaUpload.messageType) {
      case 'LOCATION':
      case 'CONTACT':
        return "Add a message (optional)";
      default:
        if (mediaUpload.status === 'READY') return "Add a caption...";
        if (mediaUpload.status === 'PROCESSING') return "Processing media...";
        if (mediaUpload.status === 'UPLOADING') return "Uploading media...";
        return "Type a message";
    }
  };

  return (
    <div className={`bg-[#202c33] border-t border-[#2a3942] ${className}`}>
      {/* Media Preview */}
      {mediaUpload && (
        <MediaPreview
          mediaUpload={mediaUpload}
          onRemove={handleRemoveMedia}
          onImageProcess={handleImageProcess}
        />
      )}

      <div className="flex items-end gap-3 px-4 py-3">
        {/* Media Attachment Menu */}
        <MediaAttachmentMenu
          onMediaTypeSelect={handleMediaTypeSelect}
          disabled={sending || mediaUpload?.status === 'UPLOADING' || mediaUpload?.status === 'PROCESSING'}
        />

        {/* Text Input Container */}
        <div className="flex-1 relative bg-[#2a3942] rounded-[18px] border border-[#3b4a54] min-h-[40px] flex items-center">
          <textarea
            ref={textareaRef}
            value={textContent}
            onChange={handleTextChange}
            onKeyDown={handleKeyPress}
            placeholder={getPlaceholderText()}
            disabled={sending || mediaUpload?.status === 'UPLOADING'}
            className="w-full bg-transparent px-4 py-2.5 pr-10 text-[15px] resize-none focus:outline-none placeholder:text-[#8696a0] text-[#e9edef] disabled:opacity-50 leading-5"
            style={{ 
              height: '40px',
              minHeight: '40px',
              maxHeight: '120px'
            }}
            rows={1}
          />
          
          {/* Emoji Button */}
          <Button
            variant="ghost"
            size="sm"
            className="absolute right-3 top-1/2 -translate-y-1/2 h-6 w-6 p-0 hover:bg-[#3b4a54] text-[#8696a0] hover:text-[#e9edef] rounded-full flex-shrink-0"
            disabled={sending || mediaUpload?.status === 'UPLOADING'}
            title="Insert emoji"
          >
            <Smile className="h-4 w-4" />
          </Button>
        </div>

        {/* Microphone or Send Button */}
        {showMicButton ? (
          <Button
            onClick={handleVoiceRecord}
            size="sm"
            className={`h-10 w-10 p-0 rounded-full flex-shrink-0 transition-all ${
              isRecording
                ? 'bg-red-600 hover:bg-red-700 text-white'
                : 'bg-[#2a3942] hover:bg-[#3b4a54] text-[#8696a0]'
            }`}
            title="Record voice message"
          >
            <Mic className={`h-5 w-5 ${isRecording ? 'animate-pulse' : ''}`} />
          </Button>
        ) : (
          <Button
            onClick={handleSend}
            disabled={!canSend}
            size="sm"
            className={`h-10 w-10 p-0 rounded-full flex-shrink-0 transition-all ${
              canSend
                ? 'bg-[#00a884] hover:bg-[#008069] text-white'
                : 'bg-[#2a3942] text-[#8696a0] cursor-not-allowed'
            }`}
            title="Send message"
          >
            {sending ? (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
            ) : (
              <Send className="h-5 w-5" />
            )}
          </Button>
        )}
      </div>

      {/* Location Dialog */}
      <LocationDialog
        open={isLocationDialogOpen}
        onOpenChange={setIsLocationDialogOpen}
        onLocationSelect={handleLocationSelect}
      />

      {/* Contact Dialog */}
      <ContactDialog
        open={isContactDialogOpen}
        onOpenChange={setIsContactDialogOpen}
        onContactSelect={handleContactSelect}
      />
    </div>
  );
}; 