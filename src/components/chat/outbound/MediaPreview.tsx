import React, { useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { removeBackground, compressImage, isImageFile } from '@/utils/imageProcessing';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  FileImage,
  FileVideo,
  FileText,
  MapPin,
  Users,
  Smile,
  X,
  ZoomIn,
  Play,
  Volume2,
  Phone,
  Mail,
  Building,
  Calendar,
  Globe,
  Loader2,
  Upload,
  CheckCircle,
  Scissors,
  Download,
  MoreHorizontal
} from 'lucide-react';

export type MediaStatus = 'UPLOADING' | 'PROCESSING' | 'READY' | 'FAILED';

export interface BaseMediaUpload {
  s3Key: string;
  fileName: string;
  mediaId?: string;
  status: MediaStatus;
  fileSize: number;
  mediaType: string;
  messageType: 'IMAGE' | 'VIDEO' | 'AUDIO' | 'DOCUMENT' | 'STICKER' | 'LOCATION' | 'CONTACT';
  progress?: number;
  previewUrl?: string;
}

export interface ImageUpload extends BaseMediaUpload {
  messageType: 'IMAGE';
}

export interface VideoUpload extends BaseMediaUpload {
  messageType: 'VIDEO';
  duration?: number;
  thumbnail?: string;
}

export interface AudioUpload extends BaseMediaUpload {
  messageType: 'AUDIO';
  duration?: number;
  waveform?: number[];
}

export interface DocumentUpload extends BaseMediaUpload {
  messageType: 'DOCUMENT';
  pageCount?: number;
}

export interface StickerUpload extends BaseMediaUpload {
  messageType: 'STICKER';
}

export interface LocationUpload {
  messageType: 'LOCATION';
  latitude: number;
  longitude: number;
  name?: string;
  address?: string;
  status: MediaStatus;
}

export interface ContactUpload {
  messageType: 'CONTACT';
  name: string;
  phoneNumber?: string;
  email?: string;
  organization?: string;
  address?: string;
  birthday?: string;
  url?: string;
  status: MediaStatus;
}

export type MediaUpload = ImageUpload | VideoUpload | AudioUpload | DocumentUpload | StickerUpload | LocationUpload | ContactUpload;

interface MediaPreviewProps {
  mediaUpload: MediaUpload;
  onRemove: () => void;
  onPreview?: (url: string) => void;
  onImageProcess?: (processedFile: File) => void;
  className?: string;
}

export const MediaPreview: React.FC<MediaPreviewProps> = ({
  mediaUpload,
  onRemove,
  onPreview,
  onImageProcess,
  className = ""
}) => {
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getStatusDisplay = () => {
    switch (mediaUpload.status) {
      case 'UPLOADING':
        return (
          <div className="flex items-center gap-2 text-blue-600">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>Uploading... {'progress' in mediaUpload ? mediaUpload.progress || 0 : 0}%</span>
          </div>
        );
      case 'PROCESSING':
        return (
          <div className="flex items-center gap-2 text-orange-600">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>Processing...</span>
          </div>
        );
      case 'READY':
        return (
          <div className="flex items-center gap-2 text-green-600">
            <CheckCircle className="h-4 w-4" />
            <span>Ready to send!</span>
          </div>
        );
      case 'FAILED':
        return (
          <div className="flex items-center gap-2 text-red-600">
            <X className="h-4 w-4" />
            <span>Upload failed</span>
          </div>
        );
      default:
        return null;
    }
  };

  const handlePreviewClick = useCallback((url: string) => {
    setPreviewUrl(url);
    setIsPreviewOpen(true);
    onPreview?.(url);
  }, [onPreview]);

  const handleRemoveBackground = useCallback(async () => {
    if (mediaUpload.messageType !== 'IMAGE' || !('file' in mediaUpload) || !mediaUpload.file) return;

    setIsProcessing(true);
    try {
      const processedFile = await removeBackground(mediaUpload.file);
      onImageProcess?.(processedFile);
    } catch (error) {
      console.error('Failed to remove background:', error);
    } finally {
      setIsProcessing(false);
    }
  }, [mediaUpload, onImageProcess]);

  const handleCompressImage = useCallback(async () => {
    if (mediaUpload.messageType !== 'IMAGE' || !('file' in mediaUpload) || !mediaUpload.file) return;

    setIsProcessing(true);
    try {
      const compressedFile = await compressImage(mediaUpload.file, { quality: 0.6 });
      onImageProcess?.(compressedFile);
    } catch (error) {
      console.error('Failed to compress image:', error);
    } finally {
      setIsProcessing(false);
    }
  }, [mediaUpload, onImageProcess]);

  const renderThumbnail = () => {
    switch (mediaUpload.messageType) {
      case 'IMAGE':
        const imageUpload = mediaUpload as ImageUpload;
        return (
          <div className="relative w-full h-full cursor-pointer group" 
               onClick={() => imageUpload.previewUrl && handlePreviewClick(imageUpload.previewUrl)}>
            {imageUpload.previewUrl && (imageUpload.status === 'READY' || imageUpload.status === 'PROCESSING') ? (
              <>
                <img 
                  src={imageUpload.previewUrl} 
                  alt={imageUpload.fileName}
                  className="w-full h-full object-cover transition-opacity group-hover:opacity-75"
                />
                <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                  <ZoomIn className="h-5 w-5 text-white drop-shadow-lg" />
                </div>
              </>
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <FileImage className="h-6 w-6 text-gray-400" />
              </div>
            )}
          </div>
        );

      case 'VIDEO':
        const videoUpload = mediaUpload as VideoUpload;
        return (
          <div className="relative w-full h-full cursor-pointer group"
               onClick={() => videoUpload.previewUrl && handlePreviewClick(videoUpload.previewUrl)}>
            {videoUpload.thumbnail || videoUpload.previewUrl ? (
              <>
                <img 
                  src={videoUpload.thumbnail || videoUpload.previewUrl} 
                  alt={videoUpload.fileName}
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30">
                  <Play className="h-8 w-8 text-white drop-shadow-lg" />
                </div>
                {videoUpload.duration && (
                  <div className="absolute bottom-1 right-1 bg-black bg-opacity-70 text-white text-xs px-1 rounded">
                    {formatDuration(videoUpload.duration)}
                  </div>
                )}
              </>
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <FileVideo className="h-6 w-6 text-gray-400" />
              </div>
            )}
          </div>
        );

      case 'AUDIO':
        return (
          <div className="w-full h-full flex items-center justify-center bg-green-50">
            <Volume2 className="h-6 w-6 text-green-600" />
          </div>
        );

      case 'DOCUMENT':
        return (
          <div className="w-full h-full flex items-center justify-center bg-blue-50">
            <FileText className="h-6 w-6 text-blue-600" />
          </div>
        );

      case 'STICKER':
        const stickerUpload = mediaUpload as StickerUpload;
        return (
          <div className="relative w-full h-full cursor-pointer group"
               onClick={() => stickerUpload.previewUrl && handlePreviewClick(stickerUpload.previewUrl)}>
            {stickerUpload.previewUrl ? (
              <img 
                src={stickerUpload.previewUrl} 
                alt="Sticker"
                className="w-full h-full object-contain"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <Smile className="h-6 w-6 text-yellow-600" />
              </div>
            )}
          </div>
        );

      case 'LOCATION':
        return (
          <div className="w-full h-full flex items-center justify-center bg-orange-50">
            <MapPin className="h-6 w-6 text-orange-600" />
          </div>
        );

      case 'CONTACT':
        return (
          <div className="w-full h-full flex items-center justify-center bg-indigo-50">
            <Users className="h-6 w-6 text-indigo-600" />
          </div>
        );

      default:
        return (
          <div className="w-full h-full flex items-center justify-center">
            <FileImage className="h-6 w-6 text-gray-400" />
          </div>
        );
    }
  };

  const renderDetails = () => {
    switch (mediaUpload.messageType) {
      case 'LOCATION':
        const locationUpload = mediaUpload as LocationUpload;
        return (
          <div>
            <p className="font-medium text-gray-900 truncate">
              {locationUpload.name || 'Shared Location'}
            </p>
            <p className="text-sm text-gray-500 truncate">
              {locationUpload.address || `${locationUpload.latitude}, ${locationUpload.longitude}`}
            </p>
          </div>
        );

      case 'CONTACT':
        const contactUpload = mediaUpload as ContactUpload;
        return (
          <div>
            <p className="font-medium text-gray-900 truncate">{contactUpload.name}</p>
            <p className="text-sm text-gray-500 truncate">
              {contactUpload.phoneNumber || contactUpload.email || 'Contact'}
            </p>
          </div>
        );

      default:
        if ('fileName' in mediaUpload && 'fileSize' in mediaUpload) {
          return (
            <div>
              <p className="font-medium text-gray-900 truncate">{mediaUpload.fileName}</p>
              <p className="text-sm text-gray-500">{formatFileSize(mediaUpload.fileSize)}</p>
              {'duration' in mediaUpload && mediaUpload.duration && (
                <p className="text-sm text-gray-500">Duration: {formatDuration(mediaUpload.duration)}</p>
              )}
            </div>
          );
        }
        return null;
    }
  };

  const renderContactDetails = () => {
    if (mediaUpload.messageType !== 'CONTACT') return null;
    
    const contact = mediaUpload as ContactUpload;
    return (
      <div className="space-y-2">
        {contact.phoneNumber && (
          <div className="flex items-center gap-2 text-sm">
            <Phone className="h-4 w-4 text-gray-400" />
            <span>{contact.phoneNumber}</span>
          </div>
        )}
        {contact.email && (
          <div className="flex items-center gap-2 text-sm">
            <Mail className="h-4 w-4 text-gray-400" />
            <span>{contact.email}</span>
          </div>
        )}
        {contact.organization && (
          <div className="flex items-center gap-2 text-sm">
            <Building className="h-4 w-4 text-gray-400" />
            <span>{contact.organization}</span>
          </div>
        )}
        {contact.birthday && (
          <div className="flex items-center gap-2 text-sm">
            <Calendar className="h-4 w-4 text-gray-400" />
            <span>{contact.birthday}</span>
          </div>
        )}
        {contact.url && (
          <div className="flex items-center gap-2 text-sm">
            <Globe className="h-4 w-4 text-gray-400" />
            <span className="truncate">{contact.url}</span>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={`px-4 py-3 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-200 ${className}`}>
      <div className="flex items-start gap-3">
        {/* Media thumbnail */}
        <div className="relative flex-shrink-0">
          <div className="w-16 h-16 bg-gray-100 rounded-lg overflow-hidden border-2 border-gray-200">
            {renderThumbnail()}
          </div>
          
          {/* Status overlay */}
          {mediaUpload.status === 'UPLOADING' && (
            <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center">
              <Upload className="h-6 w-6 text-white animate-bounce" />
            </div>
          )}
          {mediaUpload.status === 'PROCESSING' && (
            <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center">
              <Loader2 className="h-6 w-6 text-white animate-spin" />
            </div>
          )}
          {mediaUpload.status === 'READY' && (
            <div className="absolute -top-1 -right-1 bg-green-600 rounded-full p-1">
              <CheckCircle className="h-4 w-4 text-white" />
            </div>
          )}
        </div>

        {/* Media info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              {renderDetails()}
              <div className="mt-1">
                {getStatusDisplay()}
              </div>
              {/* Show additional contact details if it's a contact */}
              {mediaUpload.messageType === 'CONTACT' && (
                <div className="mt-2">
                  {renderContactDetails()}
                </div>
              )}
            </div>
            
            {/* Image processing buttons */}
            {mediaUpload.messageType === 'IMAGE' && mediaUpload.status === 'READY' && onImageProcess && (
              <div className="flex items-center gap-1 ml-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleRemoveBackground}
                  disabled={isProcessing}
                  className="p-1 h-8 w-8 text-gray-400 hover:text-blue-500 hover:bg-blue-50"
                  title="Remove background"
                >
                  {isProcessing ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Scissors className="h-4 w-4" />
                  )}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCompressImage}
                  disabled={isProcessing}
                  className="p-1 h-8 w-8 text-gray-400 hover:text-green-500 hover:bg-green-50"
                  title="Compress image"
                >
                  <Download className="h-4 w-4" />
                </Button>
              </div>
            )}

            {/* Remove button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={onRemove}
              disabled={mediaUpload.status === 'UPLOADING'}
              className="p-1 h-8 w-8 text-gray-400 hover:text-red-500 hover:bg-red-50 ml-2"
              title="Remove media"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Progress bar for uploading only */}
          {mediaUpload.status === 'UPLOADING' && 'progress' in mediaUpload && mediaUpload.progress !== undefined && (
            <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                style={{ width: `${mediaUpload.progress}%` }}
              />
            </div>
          )}
        </div>
      </div>

      {/* Preview Modal */}
      <Dialog open={isPreviewOpen} onOpenChange={setIsPreviewOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] p-0">
          <DialogHeader className="p-6 pb-0">
            <DialogTitle className="text-lg font-semibold">
              {'fileName' in mediaUpload ? mediaUpload.fileName : 'Media Preview'}
            </DialogTitle>
          </DialogHeader>
          <div className="p-6 flex items-center justify-center">
            {previewUrl && (
              <img 
                src={previewUrl} 
                alt="Preview" 
                className="max-w-full max-h-[70vh] object-contain rounded-lg"
              />
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}; 