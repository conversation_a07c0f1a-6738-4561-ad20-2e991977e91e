// Mobile-First Floating Navbar Component
// Enterprise-grade floating bottom navigation optimized for mobile devices

import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { LucideIcon, BarChart3, MessageSquare, ShoppingBag, MessageCircle, LogOut, Menu, X, Users } from 'lucide-react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { useAuth } from '@/hooks/useAuth';
import { useResponsiveBreakpoint } from './responsive-grid';

// ============================================================================
// Mobile-First Component Variants
// ============================================================================

const floatingNavbarVariants = cva(
  "relative mx-auto z-50 transition-all duration-300 ease-out",
  {
    variants: {
      variant: {
        default: "bg-card/95 backdrop-blur-lg border border-border/50 shadow-2xl shadow-black/10",
        glass: "bg-white/10 backdrop-blur-md border border-white/20 shadow-2xl shadow-black/20",
        dark: "bg-black/90 backdrop-blur-lg border border-white/10 shadow-2xl shadow-black/30",
        whatsapp: "bg-[#16242d] border border-[#1e252a]  shadow-2xl shadow-black/30",
        gradient: "bg-gradient-to-r from-primary/90 to-secondary/90 backdrop-blur-lg border border-white/20 shadow-2xl shadow-primary/20",
      },
      size: {
        sm: "px-2 py-1.5 sm:px-4 sm:py-2",
        default: "px-3 py-2 sm:px-6 sm:py-3",
        lg: "px-4 py-2.5 sm:px-8 sm:py-4",
      },
      rounded: {
        default: "rounded-xl sm:rounded-2xl",
        full: "rounded-full",
        lg: "rounded-2xl sm:rounded-3xl",
      },
      position: {
        bottom: "fixed bottom-4 left-1/2 transform -translate-x-1/2",
        top: "fixed top-4 left-1/2 transform -translate-x-1/2",
        static: "relative",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      rounded: "default",
      position: "bottom",
    },
  }
);

const navItemVariants = cva(
  "relative flex flex-col items-center justify-center transition-all duration-300 cursor-pointer group touch-manipulation",
  {
    variants: {
      size: {
        sm: "px-2 py-1.5 min-w-[50px] sm:px-3 sm:py-2 sm:min-w-[60px]",
        default: "px-3 py-2 min-w-[60px] sm:px-4 sm:py-3 sm:min-w-[70px]",
        lg: "px-4 py-2.5 min-w-[70px] sm:px-5 sm:py-4 sm:min-w-[80px]",
      },
      active: {
        true: "scale-110 sm:scale-105",
        false: "hover:scale-105 active:scale-95",
      },
      mobile: {
        true: "tap-highlight-transparent select-none",
        false: "",
      },
    },
    defaultVariants: {
      size: "default",
      active: false,
      mobile: true,
    },
  }
);

// ============================================================================
// Enhanced Component Types
// ============================================================================

export interface NavItem {
  id: string;
  label: string;
  icon: LucideIcon;
  path: string;
  badge?: {
    count?: number;
    text?: string;
    variant?: 'default' | 'secondary' | 'destructive' | 'outline';
    pulse?: boolean;
  };
  disabled?: boolean;
  mobileOnly?: boolean;
  desktopOnly?: boolean;
}

export interface FloatingNavbarProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof floatingNavbarVariants> {
  items: NavItem[];
  onItemClick?: (item: NavItem) => void;
  showLabels?: boolean;
  animated?: boolean;
  glowEffect?: boolean;
  hapticFeedback?: boolean;
  collapsible?: boolean;
  maxItems?: number;
}

// ============================================================================
// Mobile-Optimized Badge Component
// ============================================================================

const MobileNavItemBadge: React.FC<{ 
  badge: NonNullable<NavItem['badge']>;
  size: 'sm' | 'default' | 'lg';
  isMobile: boolean;
}> = ({ badge, size, isMobile }) => {
  const badgeSize = isMobile 
    ? (size === 'sm' ? 'h-4 w-4 text-[10px]' : 'h-5 w-5 text-[11px]') 
    : (size === 'sm' ? 'h-4 w-4 text-[10px]' : size === 'lg' ? 'h-6 w-6 text-xs' : 'h-5 w-5 text-xs');
  
  if (badge.count !== undefined) {
    if (badge.count === 0) return null;
    
    return (
      <div className={cn(
        "absolute -top-2 -right-2 rounded-full bg-red-500 text-white font-bold flex items-center justify-center z-10",
        badgeSize,
        badge.pulse && "animate-pulse",
        badge.count > 99 && "px-1",
        isMobile && "shadow-lg"
      )}>
        {badge.count > 99 ? '99+' : badge.count}
      </div>
    );
  }

  if (badge.text) {
    return (
      <Badge 
        variant={badge.variant} 
        className={cn(
          "absolute -top-2 -right-2 text-[10px] h-4 px-1.5 z-10",
          badge.pulse && "animate-pulse",
          isMobile && "shadow-md"
        )}
      >
        {badge.text}
      </Badge>
    );
  }

  // Simple dot indicator
  return (
    <div className={cn(
      "absolute -top-1 -right-1 w-2.5 h-2.5 bg-red-500 rounded-full z-10",
      badge.pulse && "animate-ping",
      isMobile && "shadow-lg"
    )} />
  );
};

// ============================================================================
// Mobile-Optimized Nav Item Component
// ============================================================================

const MobileNavItemComponent: React.FC<{
  item: NavItem;
  isActive: boolean;
  size: 'sm' | 'default' | 'lg';
  showLabels: boolean;
  variant: 'default' | 'glass' | 'dark' | 'gradient' | 'whatsapp';
  isMobile: boolean;
  hapticFeedback: boolean;
  onClick: () => void;
}> = ({ item, isActive, size, showLabels, variant, isMobile, hapticFeedback, onClick }) => {
  const Icon = item.icon;
  
  const handleClick = () => {
    // Haptic feedback for mobile devices
    if (hapticFeedback && isMobile && 'vibrate' in navigator) {
      navigator.vibrate(50);
    }
    onClick();
  };

  const getIconColor = () => {
    if (item.disabled) return 'text-muted-foreground/50';
    if (isActive) {
      switch (variant) {
        case 'gradient': return 'text-white';
        case 'dark': return 'text-white';
        case 'glass': return 'text-white';
        case 'whatsapp': return 'text-white';
        default: return 'text-primary';
      }
    }
    switch (variant) {
      case 'gradient': return 'text-white/70 group-hover:text-white';
      case 'dark': return 'text-white/70 group-hover:text-white';
      case 'glass': return 'text-white/70 group-hover:text-white';
      case 'whatsapp': return 'text-white/70 group-hover:text-white';
      default: return 'text-muted-foreground group-hover:text-foreground';
    }
  };

  const getLabelColor = () => {
    if (item.disabled) return 'text-muted-foreground/50';
    if (isActive) {
      switch (variant) {
        case 'gradient': return 'text-white';
        case 'dark': return 'text-white';
        case 'glass': return 'text-white';
        case 'whatsapp': return 'text-white';
        default: return 'text-primary';
      }
    }
    switch (variant) {
      case 'gradient': return 'text-white/70 group-hover:text-white';
      case 'dark': return 'text-white/70 group-hover:text-white';
      case 'glass': return 'text-white/70 group-hover:text-white';
      case 'whatsapp': return 'text-white/70 group-hover:text-white';
      default: return 'text-muted-foreground group-hover:text-foreground';
    }
  };

  const iconSize = isMobile 
    ? (size === 'sm' ? 'h-4 w-4' : 'h-5 w-5') 
    : (size === 'sm' ? 'h-4 w-4' : size === 'lg' ? 'h-6 w-6' : 'h-5 w-5');
  
  const labelSize = isMobile 
    ? (size === 'sm' ? 'text-xs' : 'text-xs') 
    : (size === 'sm' ? 'text-xs' : size === 'lg' ? 'text-sm' : 'text-xs');

  return (
    <div
      className={cn(
        navItemVariants({ size, active: isActive, mobile: isMobile }),
        item.disabled && "cursor-not-allowed opacity-50",
        !item.disabled && "hover:scale-105 active:scale-95",
        isMobile && "min-h-[44px]", // iOS touch target recommendation
        isActive && "bg-primary/10 rounded-lg"
      )}
      onClick={item.disabled ? undefined : handleClick}
      role="button"
      tabIndex={item.disabled ? -1 : 0}
      aria-label={item.label}
      aria-current={isActive ? 'page' : undefined}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          handleClick();
        }
      }}
    >
      {/* Icon */}
      <div className="relative">
        <Icon 
          className={cn(
            iconSize,
            getIconColor(),
            "transition-colors duration-200",
            isActive && "drop-shadow-sm"
          )} 
        />
        
        {/* Badge */}
        {item.badge && (
          <MobileNavItemBadge 
            badge={item.badge} 
            size={size} 
            isMobile={isMobile}
          />
        )}
      </div>
      
      {/* Label */}
      {showLabels && (
        <span className={cn(
          labelSize,
          getLabelColor(),
          "font-medium leading-none mt-1 transition-colors duration-200 text-center",
          isMobile && "max-w-[60px] truncate"
        )}>
          {item.label}
        </span>
      )}
      
      {/* Active indicator */}
      {isActive && (
        <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary rounded-full" />
      )}
    </div>
  );
};

// ============================================================================
// Mobile-First Floating Navbar Component
// ============================================================================

export const FloatingNavbar: React.FC<FloatingNavbarProps> = ({
  className,
  variant,
  size,
  rounded,
  position,
  items,
  onItemClick,
  showLabels = true,
  animated = true,
  glowEffect = false,
  hapticFeedback = true,
  collapsible = false,
  maxItems = 5,
  ...props
}) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { isMobile } = useResponsiveBreakpoint();
  const [isCollapsed, setIsCollapsed] = React.useState(false);
  
  // Filter items based on device type
  const filteredItems = items.filter(item => {
    if (isMobile && item.desktopOnly) return false;
    if (!isMobile && item.mobileOnly) return false;
    return true;
  });

  // Handle item overflow for mobile
  const visibleItems = collapsible && filteredItems.length > maxItems 
    ? filteredItems.slice(0, maxItems - 1)
    : filteredItems;

  const overflowItems = collapsible && filteredItems.length > maxItems
    ? filteredItems.slice(maxItems - 1)
    : [];

  const handleItemClick = (item: NavItem) => {
    if (item.disabled) return;
    
    if (onItemClick) {
      onItemClick(item);
    } else {
      navigate(item.path);
    }
  };

  const isItemActive = (item: NavItem) => {
    if (item.path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(item.path);
  };

  return (
    <div
      className={cn(
        floatingNavbarVariants({ variant, size, rounded, position }),
        glowEffect && "shadow-lg shadow-primary/20",
        animated && "animate-slide-in-from-bottom",
        className
      )}
      {...props}
    >
      <div className="flex items-center justify-center gap-1 sm:gap-2">
        {/* Visible Navigation Items */}
        {visibleItems.map((item) => (
          <MobileNavItemComponent
            key={item.id}
            item={item}
            isActive={isItemActive(item)}
            size={size || 'default'}
            showLabels={showLabels}
            variant={variant || 'default'}
            isMobile={isMobile}
            hapticFeedback={hapticFeedback}
            onClick={() => handleItemClick(item)}
          />
        ))}

        {/* Overflow Menu */}
        {overflowItems.length > 0 && (
          <div className="relative">
            <MobileNavItemComponent
              item={{
                id: 'overflow',
                label: 'More',
                icon: isCollapsed ? X : Menu,
                path: '',
                disabled: false,
              }}
              isActive={false}
              size={size || 'default'}
              showLabels={showLabels}
              variant={variant || 'default'}
              isMobile={isMobile}
              hapticFeedback={hapticFeedback}
              onClick={() => setIsCollapsed(!isCollapsed)}
            />
            
            {/* Overflow Menu Items */}
            {isCollapsed && (
              <div className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 bg-card border border-border/50 rounded-lg shadow-lg p-2 min-w-[150px]">
                {overflowItems.map((item) => (
                  <button
                    key={item.id}
                    onClick={() => {
                      handleItemClick(item);
                      setIsCollapsed(false);
                    }}
                    className="w-full flex items-center gap-3 px-3 py-2 text-sm rounded-md hover:bg-muted/50 transition-colors"
                  >
                    <item.icon className="h-4 w-4" />
                    {item.label}
                    {item.badge && (
                      <MobileNavItemBadge
                        badge={item.badge}
                        size="sm"
                        isMobile={isMobile}
                      />
                    )}
                  </button>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

// ============================================================================
// Enhanced Portal Floating Navbar
// ============================================================================

export interface PortalFloatingNavbarProps extends Omit<FloatingNavbarProps, 'items'> {
  badgeCounts?: {
    conversations?: number;
    shopifyIntegration?: number;
    whatsappConnections?: number;
    analytics?: number;
    crm?: number;
  };
  showLogout?: boolean;
}

export const PortalFloatingNavbar: React.FC<PortalFloatingNavbarProps> = ({
  badgeCounts = {},
  showLogout = true,
  ...props
}) => {
  const { signOut } = useAuth();
  const { isMobile } = useResponsiveBreakpoint();
  const navigate = useNavigate();

  const handleLogout = async () => {
    try {
      const success = await signOut();
      if (success) {
        navigate('/auth/login');
      }
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // Enhanced navigation items with better mobile optimization
  const navItems: NavItem[] = [
    {
      id: 'analytics',
      label: 'Analytics',
      icon: BarChart3,
      path: '/portal/analytics',
      badge: badgeCounts.analytics ? { count: badgeCounts.analytics, pulse: true } : undefined,
    },
    {
      id: 'conversations',
      label: isMobile ? 'Chats' : 'Conversations',
      icon: MessageSquare,
      path: '/portal/chat',
      badge: badgeCounts.conversations ? { count: badgeCounts.conversations, pulse: true } : undefined,
    },
    {
      id: 'crm',
      label: 'CRM',
      icon: Users,
      path: '/portal/crm',
      badge: badgeCounts.crm ? { count: badgeCounts.crm, pulse: true } : undefined,
    },
    {
      id: 'shopify-integration',
      label: isMobile ? 'Shopify' : 'Shopify Integration',
      icon: ShoppingBag,
      path: '/portal/shopify-integration',
      badge: badgeCounts.shopifyIntegration ? { count: badgeCounts.shopifyIntegration, pulse: true } : undefined,
    },
    {
      id: 'whatsapp-connections',
      label: isMobile ? 'WhatsApp' : 'WhatsApp Business',
      icon: MessageCircle,
      path: '/portal/whatsapp-connections',
      badge: badgeCounts.whatsappConnections ? { count: badgeCounts.whatsappConnections, pulse: true } : undefined,
    },
    ...(showLogout ? [{
      id: 'logout',
      label: 'Logout',
      icon: LogOut,
      path: '',
      mobileOnly: false,
    }] : []),
  ];

  const handleItemClick = (item: NavItem) => {
    if (item.id === 'logout') {
      handleLogout();
    } else if (item.path) {
      navigate(item.path);
    }
  };

  return (
    <FloatingNavbar
      items={navItems}
      onItemClick={handleItemClick}
      showLabels={true}
      animated={true}
      glowEffect={false}
      hapticFeedback={true}
      collapsible={isMobile}
      maxItems={isMobile ? 4 : 5}
      variant="default"
      size={isMobile ? "default" : "default"}
      {...props}
    />
  );
};

export default FloatingNavbar; 